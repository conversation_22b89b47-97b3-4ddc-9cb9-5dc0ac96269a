import { Box, createMuiTheme, Divider, <PERSON>arProgress, MuiThemeProvider, styled, ThemeProvider, withStyles } from "@mui/material";
import { ThemeProviderProps } from "@material-ui/styles/ThemeProvider";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSelector } from "react-redux";
import MultiSelectModel from "../../models/MultiSelectModel";
import { SubObjectivePercentageModel, ObjectivePercentageModel, GetCountrySpecificSubObjectiveModel } from "../../models/DashboardModel";
import { dataQualityAssessmentService } from "../../services/dataQualityAssessmentService";
import useYears from "../assessments/data-collection/useYears";
import CustomProgressBar from "../controls/CustomProgressBar";
import Dropdown from "../controls/Dropdown";
import Tabs, { WHOTab } from "../controls/Tabs";
import { dashboardService } from "../../services/dashboardService";
import { Constants } from "../../models/Constants";
import variables from './CountryDashboard.module.scss';

type CssVariables = {
    greencolor: string;
    yellowcolor: string;
    redcolor: string;
    greycolor: string;
}
////Summary
/* Country dashboard component to represent year wise objective's and subobjective's completion status */
function CountryDashboard() {

    const [objectives, setobjectives] = useState<Array<ObjectivePercentageModel>>([]);
    const [objectiveSubObjective, setobjectiveSubObjective] = useState<Array<SubObjectivePercentageModel>>([]);
    const [color, setColor] = useState("");
    const [years, setYears] = useState<Array<MultiSelectModel>>([]);
    const [countryYear, setCountryYear] = useState<string>("");
    const { t } = useTranslation();
    const errors = useSelector((state: any) => state.error);
    let selectedCountryId = sessionStorage.getItem(Constants.SessionStorageKey.SELECTED_COUNTRY);

    useEffect(() => {
        bindYears();
    }, []);

    useEffect(() => {
        //call apis for first time when years change in state
        if (countryYear !== "") {
            bindObjectivesSubObjectivesIndicators(countryYear);
        }
    }, [countryYear]);

    //bind objectives and subobjectives data for specific year.
    const bindObjectivesSubObjectivesIndicators = (year: string) => {
        let countryId = selectedCountryId || '';
        dashboardService
            .getDashboardRecords(countryId, year)
            .then((objective: Array<SubObjectivePercentageModel>) => {
                if (objective !== null) {
                    setobjectiveSubObjective(objective);
                } else {
                    setobjectiveSubObjective([]);
                }
            })
    }

    //get years list for all available assessment in logged in user's country
    const bindYears = () => {
        //get active country id of logged in user.
        let countryId = selectedCountryId || '';
        dashboardService
            .getDashboardYears(countryId)
            .then((year: Array<MultiSelectModel>) => {
                setYears(year);
                if (year.length > 0) {
                    setCountryYear(year[0].id.toString());
                    bindObjectivesSubObjectivesIndicators(year[0].id.toString());
                }
            })
    }

    // Triggers whenever user tries to change dropdown the value
    const onChangeYears = (fieldName: string, e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setCountryYear(e.currentTarget.value);
        bindObjectivesSubObjectivesIndicators(e.currentTarget.value);
    };

    // return colors for completeness core variables to highlight percentage value
    const getColorForIndicatorStatus = (value: number) => {
        const colVariables: CssVariables = JSON.parse(JSON.stringify(variables));
        // switch (true) {
        //     case value > 95 && value <= 100:
        //         return colVariables.greencolor;
        //     case value <= 95 && value >= 80:
        //         return colVariables.yellowcolor;
        //     case (value < 80 && value >= 0) || value > 100:
        //         return colVariables.redcolor;
        //     default: return colVariables.greycolor;
        // }

        switch (true) {
            case value < 50:
                return colVariables.redcolor;
            case value >= 50 && value <= 79:
                return colVariables.yellowcolor;
            case value >= 80:
                return colVariables.greencolor;
            default: return colVariables.greycolor;
        }
    }

    const getColorForSubObjectiveIndicatorStatus = (subObjective: GetCountrySpecificSubObjectiveModel) => {
        const colVariables: CssVariables = JSON.parse(JSON.stringify(variables));
        // switch (true) {
        //     case subObjective.isNotAssessed:
        //         return colVariables.greycolor;
        //     case subObjective.percentage > 95 && subObjective.percentage <= 100:
        //         return colVariables.greencolor;
        //     case subObjective.percentage <= 95 && subObjective.percentage >= 80:
        //         return colVariables.yellowcolor;
        //     case (subObjective.percentage < 80 && subObjective.percentage >= 0) || subObjective.percentage > 100:
        //         return colVariables.redcolor;
        //     default: return colVariables.greycolor;
        // }

        switch (true) {
            case subObjective.isNotAssessed:
                return colVariables.greycolor;
            case subObjective.percentage < 50:
                return colVariables.redcolor;
            case subObjective.percentage >= 50 && subObjective.percentage <= 79:
                return colVariables.yellowcolor;
            case subObjective.percentage > 80:
                return colVariables.greencolor;
            default: return colVariables.greycolor;
        }
    }

    return (

        <div className="countyr-card-wrapper">
            <div className="row justify-content-left">
                <div className="col-sm-2">
                    <div className="d-flex justify-content-center mb-3 mt-2 year-select">
                        <Dropdown
                            id="year"
                            name="year"
                            variant="outlined"
                            size="small"
                            label={t("Common.YearOfData")}
                            value={countryYear}
                            options={years}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                onChangeYears(
                                    "year",
                                    e
                                )
                            }
                            error={errors["year"] && errors["year"]}
                            helperText={errors["year"] && errors["year"]}
                        />
                    </div>
                </div>
            </div>

            {
                (objectiveSubObjective !== undefined && objectiveSubObjective.length > 0) ?
                    (<div className="row">
                        {
                            (objectiveSubObjective.map((objective: SubObjectivePercentageModel) => {
                                return (
                                    <div key={objective.id} className="col-sm-12 col-md-3">
                                        <div className="card mb-3 pt-4 pb-4 px-md-4 h-100">
                                            <CustomProgressBar key={objective.id}
                                                bgcolor={getColorForIndicatorStatus(objective.percentage)}
                                                completed={objective.percentage}
                                                name={objective.name}
                                                titleSize="0.9rem"
                                                textColor="#2b2b2b"
                                            />
                                            <Divider className="mt-3" variant="middle" light={false} />
                                            {
                                                objective.subObjective.map((subObjective: GetCountrySpecificSubObjectiveModel) => {
                                                    return (
                                                        <div key={subObjective.id} className="mt-3">
                                                            <CustomProgressBar key={subObjective.id}
                                                                bgcolor={getColorForSubObjectiveIndicatorStatus(subObjective)}
                                                                completed={subObjective.percentage}
                                                                name={subObjective.name}
                                                                titleSize="0.8rem"
                                                                textColor="#84888e"
                                                            />
                                                        </div>

                                                    )
                                                })}
                                        </div>
                                    </div>
                                )
                            }))

                        }
                    </div>) :
                    (
                        <div className="app-dashboard d-flex align-items-center justify-content-center">
                            {t("Dashboard.CountryDashboardNoData")}
                        </div>
                    )
            }
            {
                (objectiveSubObjective !== undefined && objectiveSubObjective.length > 0) ?
                    (
                        <div className="d-flex justify-content-center py-4">

                            <ul className="dashboard-legend">

                                <li><span className="met"></span>{t("Common.Met")}​​​​​</li>

                                <li><span className="not-met"></span>{t("Common.NotMet")}​​​​​</li>

                                <li><span className="partially-met"></span>{t("Common.PartiallyMet")}​​​​​</li>

                                <li><span className="not-assessed"></span>{t("Common.NotAssessed")}​​​​​</li>

                            </ul>

                        </div>
                    )
                    :
                    (<></>)
            }
        </div>
    )
}

export default CountryDashboard;
