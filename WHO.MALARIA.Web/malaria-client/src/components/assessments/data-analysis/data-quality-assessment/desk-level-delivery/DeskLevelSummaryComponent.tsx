import React, { useEffect, useState } from "react";
import classNames from "classnames";
import classes from "../../../data-collection/data-quality-assessment/dqa.module.scss";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Close";
import Breadcrumbs from "../../../data-collection/desk-review/responses/Breadcrumbs";
import { dqaService } from "../../../../../services/dqaService";
import Dropdown from "../../../../controls/Dropdown";
import {
  DeskLevelSummaryReportModel,
  SummaryDataModel,
  ConsistentTrendValue,
  ConsistentTrendHelper,
} from "../../../../../models/DQA/DeskLevelSummaryReportModel";
import MultiSelectModel from "../../../../../models/MultiSelectModel";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>utton } from "@mui/material";
import RadioButtonGroup from "../../../../controls/RadioButtonGroup";
import TextBox from "../../../../controls/TextBox";
import { useSelector } from "react-redux";
import Table from "../../../data-collection/desk-review/responses/Table";
import TableBody from "../../../data-collection/desk-review/responses/TableBody";
import TableRow from "../../../data-collection/desk-review/responses/TableRow";
import TableCell from "../../../data-collection/desk-review/responses/TableCell";
import TableHeader from "../../../data-collection/desk-review/responses/TableHeader";
import NationalLevelSummaryValidationRules from "./NationalLevelSummaryValidationRules";
import useFormValidation from "../../../../common/useFormValidation";
import { UserAssessmentPermission } from "../../../../../models/PermissionModel";
import UserMessage from "../../../../common/UserMessage";
import { DQADLSummaryResultType } from "../../../../../models/Enums";
import InfoIcon from "@mui/icons-material/Info";
import DownloadInProgressModal from "../../../../controls/DownloadInProgressModal";
import { UtilityHelper } from "../../../../../utils/UtilityHelper";
import parse from "html-react-parser";
import Tooltip from "../../../../controls/Tooltip";

//Data analysis DL DQA National Level Summary report component which renders Result and Targets for core indicators

const DeskLevelSummaryComponent = () => {
  const { t } = useTranslation();
  document.title = t("app.DQASummary");
  const location: any = useLocation();
  const navigate = useNavigate();
  const assessmentId = location?.state?.assessmentId;
  const subTabIndex = location?.state?.subTabIndex;
  const [years, setYears] = useState<Array<string>>([]);
  const [summaryYear, setSummaryYear] = useState<string>("");
  const [serviceLevelData, setServiceLevelData] = useState<SummaryDataModel>(
    SummaryDataModel.init(assessmentId)
  );
  const [summaryData, setSummaryData] = useState<SummaryDataModel>(
    SummaryDataModel.init(assessmentId)
  );
  const userPermission: UserAssessmentPermission = useSelector(
    (state: any) => state.userPermission.assessment
  );
  const {
    canEditAndSaveDeskLevelDQASummaryResult,
    canFinalizeDeskLevelDQASummaryResult,
    canExportDeskLevelDQASummary,
  } = userPermission;
  const validate = useFormValidation(NationalLevelSummaryValidationRules);
  const errors = useSelector((state: any) => state.error);
  const [isDeskLevelSummaryDataSaved, setIsDeskLevelSummaryDataSaved] =
    useState<boolean>(false);
  const [isFileDownloading, setIsFileDownloading] = useState<boolean>(false);

  //Triggers whenever user takes an action on close or back navigation click
  const onBackOrCloseButtonClick = () => {
    const state = location?.state;

    navigate("/assessment/data-analysis/dqa", {
      state: {
        ...state,
        subTabIndex,
      },
    });
  };

  // Table headers
  const tableHeadersDataQuality = [
    t(
      "Assessment.DataCollection.DataQualityAssessment.NationalDataQualityEstimates"
    ),
    t("Assessment.DataCollection.DataQualityAssessment.NationalLevelResults"),
    t("Assessment.DataCollection.DataQualityAssessment.NationalLevelTarget"),
  ];

  useEffect(() => {
    bindSummaryYears();
  }, []);

  // Debug: Log when consistency over time changes
  useEffect(() => {
    console.log(
      "Burden Reduction - Consistency over time changed:",
      summaryData.consistencyOverTimeCoreIndicators
    );
  }, [summaryData.consistencyOverTimeCoreIndicators]);

  // Calculate consistency over time for core indicators based on current summary data
  const calculateConsistencyOverTime = (
    summaryData: DeskLevelSummaryReportModel
  ) => {
    const coreIndicatorFields = [
      "malariaOutpatientProportion",
      "malariaInPatientProportion",
      "malariaInPatientDeathProportion",
      "testPositivityRate",
      "slidePositivityRate",
      "rdtPositivityRate",
      "suspectedTestProportion",
    ];

    const nonNAIndicators = coreIndicatorFields.filter(
      field => summaryData[field] !== null
    );

    return (
      nonNAIndicators.length > 0 &&
      nonNAIndicators.every(field => summaryData[field] === true)
    );
  };

  // Triggers whenever a change event if fired from radio button
  const nationalLevelTargetRadioChange = (
    fieldName: string,
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    console.log(
      `Burden Reduction - Radio change: ${fieldName} = ${e.target.value}`
    );

    const newValue = ConsistentTrendHelper.stringToBoolean(e.target.value);
    const _summaryData = {
      ...summaryData,
      [fieldName]: newValue,
    };

    // Calculate consistency over time for core indicators using the helper function
    const newConsistency = calculateConsistencyOverTime(_summaryData);
    _summaryData.consistencyOverTimeCoreIndicators = newConsistency;

    console.log(`Burden Reduction - New consistency value: ${newConsistency}`);
    setSummaryData(_summaryData);
  };

  //Helper function to get the default value for radio buttons
  const bindConsistencyOverTimeForCoreIndicators = (
    value: boolean | null,
    fieldName: string
  ) => {
    return ConsistentTrendHelper.fromBoolean(value);
  };

  // Triggers whenever the user modifies the national level target
  const nationalLevelTargetHandleChange = (
    fieldName: string,
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const _summaryData = {
      ...summaryData,
      [fieldName]: e.currentTarget.value ? +e.currentTarget.value : null,
    };

    setSummaryData(_summaryData);
  };

  // Triggers whenever the user modifies the result reason change event
  const dataQualityResultReasonChange = (
    fieldName: string,
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const _summaryData = {
      ...summaryData,
      [fieldName]: e.currentTarget.value,
    };

    setSummaryData(_summaryData);
  };

  // Triggers whenever user tries to change dropdown the value
  const onChangeYears = (
    year: string,
    reportCompleteness: string,
    reportTimeliness: string,
    variableCompleteness: string,
    variableConsistency: string,
    variableConcordance: string,
    dataQualityResultReason: string,
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    if (e.currentTarget.value != "") {
      const _summaryData = {
        ...summaryData,
        [reportCompleteness]: "",
        [reportTimeliness]: "",
        [variableCompleteness]: "",
        [variableConsistency]: "",
        [variableConcordance]: "",
        [dataQualityResultReason]: "",
        [year]: e.currentTarget.value,
      };

      setSummaryData(_summaryData);
      setSummaryYear(e.currentTarget.value);

      bindNationalDataQualityEstimates(e.currentTarget.value);
    }
  };

  // Call Desk Level-DQA indicators API service and map to GetIndicatorModel.
  const bindNationalDataQualityEstimates = (summaryYear: string) => {
    dqaService
      .getDeskLevelSummaryResult(assessmentId, summaryYear)
      .then(response => {
        setServiceLevelData(response[0]);
        if (response.length > 1) {
          // Calculate the consistency over time based on current data
          response[1].consistencyOverTimeCoreIndicators =
            calculateConsistencyOverTime(response[1]);
          setSummaryData(response[1]);
          setIsDeskLevelSummaryDataSaved(true);
        } else {
          setSummaryData(SummaryDataModel.init(assessmentId));
          setIsDeskLevelSummaryDataSaved(false);
        }
      });
  };

  //get years list for national level summary result for specific assessment.
  const bindSummaryYears = () => {
    dqaService
      .getDeskLevelSummaryYears(assessmentId)
      .then((summaryYear: Array<string>) => {
        setYears(summaryYear);
        if (summaryYear.length > 0) {
          setSummaryYear(summaryYear[0]);
          bindNationalDataQualityEstimates(summaryYear[0]);
        }
      });
  };

  const GetDeskLevelSummaryReportRequest = (isFinalized: boolean) => {
    summaryData.reportCompleteness = serviceLevelData?.reportCompleteness;
    summaryData.reportTimeliness = serviceLevelData?.reportTimeliness;
    summaryData.variableCompleteness = serviceLevelData?.variableCompleteness;
    summaryData.variableConsistency = serviceLevelData?.variableConsistency;
    summaryData.variableConcordance = serviceLevelData?.variableConcordance;
    const request: DeskLevelSummaryReportModel =
      new DeskLevelSummaryReportModel(
        new SummaryDataModel(
          assessmentId,
          summaryData?.reportCompleteness,
          summaryData?.reportTimeliness,
          summaryData?.variableCompleteness,
          summaryData?.variableConsistency,
          summaryData?.variableConcordance,
          summaryData?.malariaOutpatientProportion,
          summaryData?.malariaInPatientProportion,
          summaryData?.malariaInPatientDeathProportion,
          summaryData?.testPositivityRate,
          summaryData?.slidePositivityRate,
          summaryData?.rdtPositivityRate,
          summaryData?.suspectedTestProportion,
          DQADLSummaryResultType.NationalLevelTarget,
          parseInt(summaryYear),
          summaryData?.dataQualityResultReason,
          isFinalized
        ),
        assessmentId
      );
    return request;
  };

  // Triggers whenever user clicks on Save button
  const onSave = () => {
    const isFormValid = validate(summaryData);

    if (isFormValid) {
      const request: DeskLevelSummaryReportModel =
        GetDeskLevelSummaryReportRequest(false);
      dqaService.saveSummaryResult(request);
      setIsDeskLevelSummaryDataSaved(true);
    }
  };

  // Triggers whenever user clicks on finalize button
  const onFinalize = () => {
    const isFormValid = validate(summaryData);
    if (isFormValid) {
      const request: DeskLevelSummaryReportModel =
        GetDeskLevelSummaryReportRequest(true);

      dqaService.finalizeSummaryResult(request).then(response => {
        if (response) {
          setSummaryData({
            ...summaryData,
            isFinalized: response as boolean,
          });
        }
      });
    }
  };

  // Get CSS class for completeness core variables to highlight percentage value
  const getCssClassForNationalLevelResult = (value: any) => {
    if (!value && value !== 0) {
      return;
    }
    switch (true) {
      case value < 80 || value > 100:
        return "bg-red";
      case value >= 80 && value <= 95:
        return "bg-yellow";
      case value > 95 && value <= 100:
        return "bg-green";
      default:
        return "";
    }
  };

  // Triggers whenever user clicks on Generate Template button
  const onGenerateTemplate = () => {
    setIsFileDownloading(true);
    const downloadedTemplateName = t("app.DQADeskLevelToolReportFileName");
    dqaService
      .generateDeskLevelDQATemplate(assessmentId)
      .then((response: any) => {
        UtilityHelper.download(response, downloadedTemplateName);
        setIsFileDownloading(false);
      })
      .catch((error: any) => {
        setIsFileDownloading(false);
      });
  };

  return (
    <>
      <div className={classNames(classes.indicatorWrapper)}>
        <div className='assess-header-section d-flex pt-2 pb-2'>
          <Breadcrumbs
            parentLabel={t(
              "Assessment.DataCollection.DataQualityAssessment.DataAnalysis"
            )}
            label={t(
              "Assessment.DataCollection.DataQualityAssessment.SummaryResults"
            )}
            onNavigate={onBackOrCloseButtonClick}
          />
          <div className='ml-auto'>
            <IconButton
              onClick={onBackOrCloseButtonClick}
              title='Close'
              className='close-modal'
            >
              <CancelIcon />
            </IconButton>
          </div>
        </div>

        <div className={classNames("summary-wrapper", "p-3")}>
          <div className='row justify-content-center'>
            <div className='col-sm-3'>
              <div className='d-flex justify-content-center mb-3'>
                <Dropdown
                  id='year'
                  disabled={true}
                  name='year'
                  variant='outlined'
                  size='small'
                  label={t("Common.YearOfData")}
                  value={summaryYear}
                  options={years.map(year => {
                    return new MultiSelectModel(
                      year,
                      year.toString(),
                      false,
                      false
                    );
                  })}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                    onChangeYears(
                      "year",
                      "reportCompleteness",
                      "reportTimeliness",
                      "variableCompleteness",
                      "variableConsistency",
                      "variableConcordance",
                      "dataQualityResultReason",
                      e
                    )
                  }
                  error={years == null && errors["year"] && errors["year"]}
                  helperText={years == null && errors["year"] && errors["year"]}
                />
              </div>
            </div>
          </div>

          <Table>
            <>
              <TableHeader headers={tableHeadersDataQuality} />
              {serviceLevelData.reportCompleteness !== null ? (
                <TableBody>
                  <>
                    <TableRow>
                      <>
                        <TableCell>
                          <b>
                            1.2.1{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.ReportCompleteness"
                            )}
                          </b>
                        </TableCell>
                        <TableCell>
                          <div
                            className={`read-only-textbox  ${getCssClassForNationalLevelResult(serviceLevelData?.reportCompleteness)}`}
                          >
                            {serviceLevelData?.reportCompleteness}
                          </div>
                        </TableCell>
                        <TableCell>
                          <TextBox
                            type='number'
                            id='reportCompleteness'
                            name='reportCompleteness'
                            maxLength={3}
                            inputProps={{
                              max: 100,
                              min: 0,
                            }}
                            className={`col-form-control inputfocus`}
                            value={summaryData?.reportCompleteness}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetHandleChange(
                                "reportCompleteness",
                                e
                              );
                            }}
                            error={
                              summaryData?.reportCompleteness == null &&
                              errors["reportCompleteness"] &&
                              errors["reportCompleteness"]
                            }
                            helperText={
                              summaryData?.reportCompleteness == null &&
                              errors["reportCompleteness"] &&
                              errors["reportCompleteness"]
                            }
                          />
                        </TableCell>
                      </>
                    </TableRow>
                    <TableRow>
                      <>
                        <TableCell>
                          <b>
                            1.2.3{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.ReportTimeliness"
                            )}
                          </b>
                        </TableCell>
                        <TableCell>
                          <div
                            className={`read-only-textbox  ${getCssClassForNationalLevelResult(serviceLevelData?.reportTimeliness)}`}
                          >
                            {serviceLevelData?.reportTimeliness}
                          </div>
                        </TableCell>
                        <TableCell>
                          <TextBox
                            type='number'
                            id='reportTimeliness'
                            name='reportTimeliness'
                            maxLength={3}
                            inputProps={{
                              max: 100,
                              min: 0,
                            }}
                            className={`col-form-control inputfocus`}
                            value={summaryData?.reportTimeliness}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetHandleChange(
                                "reportTimeliness",
                                e
                              );
                            }}
                            error={
                              summaryData?.reportTimeliness == null &&
                              errors["reportTimeliness"] &&
                              errors["reportTimeliness"]
                            }
                            helperText={
                              summaryData?.reportTimeliness == null &&
                              errors["reportTimeliness"] &&
                              errors["reportTimeliness"]
                            }
                          />
                        </TableCell>
                      </>
                    </TableRow>
                    <TableRow>
                      <>
                        <TableCell>
                          <b>
                            1.2.7{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.VariableCompleteness"
                            )}
                          </b>
                        </TableCell>
                        <TableCell>
                          <div
                            className={`read-only-textbox  ${getCssClassForNationalLevelResult(serviceLevelData?.variableCompleteness)}`}
                          >
                            {serviceLevelData?.variableCompleteness}
                          </div>
                        </TableCell>
                        <TableCell>
                          <TextBox
                            type='number'
                            id='variableCompleteness'
                            name='variableCompleteness'
                            maxLength={3}
                            inputProps={{
                              max: 100,
                              min: 0,
                            }}
                            className={`col-form-control inputfocus`}
                            value={summaryData?.variableCompleteness}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetHandleChange(
                                "variableCompleteness",
                                e
                              );
                            }}
                            error={
                              summaryData?.variableCompleteness == null &&
                              errors["variableCompleteness"] &&
                              errors["variableCompleteness"]
                            }
                            helperText={
                              summaryData?.variableCompleteness == null &&
                              errors["variableCompleteness"] &&
                              errors["variableCompleteness"]
                            }
                          />
                        </TableCell>
                      </>
                    </TableRow>
                    <TableRow>
                      <>
                        <TableCell>
                          <b>
                            1.2.8{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.VariableConsistency"
                            )}
                          </b>
                        </TableCell>
                        <TableCell>
                          <div
                            className={`read-only-textbox  ${getCssClassForNationalLevelResult(serviceLevelData?.variableConsistency)}`}
                          >
                            {serviceLevelData?.variableConsistency}
                          </div>
                        </TableCell>
                        <TableCell>
                          <TextBox
                            type='number'
                            id='variableConsistency'
                            name='variableConsistency'
                            maxLength={3}
                            inputProps={{
                              max: 100,
                              min: 0,
                            }}
                            className={`col-form-control inputfocus`}
                            value={summaryData?.variableConsistency}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetHandleChange(
                                "variableConsistency",
                                e
                              );
                            }}
                            error={
                              summaryData?.variableConsistency == null &&
                              errors["variableConsistency"] &&
                              errors["variableConsistency"]
                            }
                            helperText={
                              summaryData?.variableConsistency == null &&
                              errors["variableConsistency"] &&
                              errors["variableConsistency"]
                            }
                          />
                        </TableCell>
                      </>
                    </TableRow>
                    <TableRow>
                      <>
                        <TableCell>
                          <b>
                            1.2.10{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.VariableConcordance"
                            )}
                          </b>
                        </TableCell>

                        <TableCell>
                          <div
                            className={`read-only-textbox  ${getCssClassForNationalLevelResult(serviceLevelData?.variableConcordance)}`}
                          >
                            {serviceLevelData?.variableConcordance}
                          </div>
                        </TableCell>

                        <TableCell>
                          <TextBox
                            type='number'
                            id='variableConcordance'
                            name='variableConcordance'
                            maxLength={3}
                            inputProps={{
                              max: 100,
                              min: 0,
                            }}
                            className={`col-form-control inputfocus`}
                            value={summaryData?.variableConcordance}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetHandleChange(
                                "variableConcordance",
                                e
                              );
                            }}
                            error={
                              summaryData?.variableConcordance == null &&
                              errors["variableConcordance"] &&
                              errors["variableConcordance"]
                            }
                            helperText={
                              summaryData?.variableConcordance == null &&
                              errors["variableConcordance"] &&
                              errors["variableConcordance"]
                            }
                          />
                        </TableCell>
                      </>
                    </TableRow>

                    <TableRow>
                      <>
                        <TableCell>
                          <b>
                            1.2.9{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.ConsitentOverTime"
                            )}
                          </b>
                          <IconButton className='grid-icon-button'>
                            <Tooltip
                              content={t(
                                "Assessment.DataAnalysis.DQASummaryTooltip"
                              )}
                              isHtml
                            >
                              <InfoIcon fontSize='small' />
                            </Tooltip>
                          </IconButton>
                          {
                            //Show error message if Consistency Overtime Core Indicators aren't selected
                            (errors["malariaOutpatientProportion"] ||
                              errors["malariaInPatientProportion"] ||
                              errors["malariaInPatientDeathProportion"] ||
                              errors["testPositivityRate"] ||
                              errors["slidePositivityRate"] ||
                              errors["rdtPositivityRate"] ||
                              errors["suspectedTestProportion"]) && (
                              <span className='Mui-error d-flex mb-2'>
                                *
                                {t(
                                  "Assessment.DataCollection.DataQualityAssessment.ConsitentOverTimeResponseError"
                                )}
                              </span>
                            )
                          }
                        </TableCell>

                        <TableCell>
                          <div className='disableContent'>
                            <RadioButtonGroup
                              id='consistencyOverTimeCoreIndicators'
                              name='consistencyOverTimeCoreIndicators'
                              color='primary'
                              key={`consistency-${summaryData.consistencyOverTimeCoreIndicators}`}
                              value={
                                summaryData.consistencyOverTimeCoreIndicators
                              }
                              options={[
                                new MultiSelectModel(
                                  true,
                                  t("indicators-responses:Common:Met")
                                ),
                                new MultiSelectModel(
                                  false,
                                  t("indicators-responses:Common:NotMet")
                                ),
                              ]}
                            />
                          </div>
                        </TableCell>
                        <TableCell>{}</TableCell>
                      </>
                    </TableRow>
                    <TableRow className='border'>
                      <>
                        <TableCell>
                          {t("Assessment.DataAnalysis.DQASummaryInfoText")}
                        </TableCell>
                        <TableCell>
                          <b>
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.ConsistentTrend"
                            )}
                          </b>
                        </TableCell>
                      </>
                    </TableRow>
                    <TableRow>
                      <>
                        <TableCell>
                          <i>
                            1.{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.MalariaOutpatients"
                            )}
                          </i>
                        </TableCell>

                        <TableCell>
                          <RadioButtonGroup
                            id='malariaOutpatientProportion'
                            name='malariaOutpatientProportion'
                            color='primary'
                            options={[
                              new MultiSelectModel(
                                ConsistentTrendValue.Yes,
                                t("indicators-responses:Common:Yes")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.No,
                                t("indicators-responses:Common:No")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.NA,
                                t("indicators-responses:Common:NA")
                              ),
                            ]}
                            value={ConsistentTrendHelper.fromBoolean(
                              summaryData?.malariaOutpatientProportion
                            )}
                            defaultValue={bindConsistencyOverTimeForCoreIndicators(
                              summaryData?.malariaOutpatientProportion,
                              "malariaOutpatientProportion"
                            )}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetRadioChange(
                                "malariaOutpatientProportion",
                                e
                              );
                            }}
                            error={
                              summaryData?.malariaOutpatientProportion ==
                                null &&
                              errors["malariaOutpatientProportion"] &&
                              errors["malariaOutpatientProportion"]
                            }
                            helperText={
                              summaryData?.malariaOutpatientProportion ==
                                null &&
                              errors["malariaOutpatientProportion"] &&
                              errors["malariaOutpatientProportion"]
                            }
                          />
                        </TableCell>

                        <TableCell>{}</TableCell>
                      </>
                    </TableRow>
                    <TableRow>
                      <>
                        <TableCell>
                          <i>
                            2.{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.MalariaInpatients"
                            )}
                          </i>
                        </TableCell>

                        <TableCell>
                          <RadioButtonGroup
                            id='malariaInPatientProportion'
                            name='malariaInPatientProportion'
                            row
                            color='primary'
                            options={[
                              new MultiSelectModel(
                                ConsistentTrendValue.Yes,
                                t("indicators-responses:Common:Yes")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.No,
                                t("indicators-responses:Common:No")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.NA,
                                t("indicators-responses:Common:NA")
                              ),
                            ]}
                            value={ConsistentTrendHelper.fromBoolean(
                              summaryData?.malariaInPatientProportion
                            )}
                            defaultValue={bindConsistencyOverTimeForCoreIndicators(
                              summaryData?.malariaInPatientProportion,
                              "malariaInPatientProportion"
                            )}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetRadioChange(
                                "malariaInPatientProportion",
                                e
                              );
                            }}
                            error={
                              summaryData?.malariaInPatientProportion == null &&
                              errors["malariaInPatientProportion"] &&
                              errors["malariaInPatientProportion"]
                            }
                            helperText={
                              summaryData?.malariaInPatientProportion == null &&
                              errors["malariaInPatientProportion"] &&
                              errors["malariaInPatientProportion"]
                            }
                          />
                        </TableCell>

                        <TableCell>{}</TableCell>
                      </>
                    </TableRow>
                    <TableRow>
                      <>
                        <TableCell>
                          <i>
                            3.{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.MalariaInpatientsDeath"
                            )}
                          </i>
                        </TableCell>

                        <TableCell>
                          <RadioButtonGroup
                            id='malariaInPatientDeathProportion'
                            name='malariaInPatientDeathProportion'
                            row
                            color='primary'
                            options={[
                              new MultiSelectModel(
                                ConsistentTrendValue.Yes,
                                t("indicators-responses:Common:Yes")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.No,
                                t("indicators-responses:Common:No")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.NA,
                                t("indicators-responses:Common:NA")
                              ),
                            ]}
                            value={ConsistentTrendHelper.fromBoolean(
                              summaryData?.malariaInPatientDeathProportion
                            )}
                            defaultValue={bindConsistencyOverTimeForCoreIndicators(
                              summaryData?.malariaInPatientDeathProportion,
                              "malariaInPatientDeathProportion"
                            )}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetRadioChange(
                                "malariaInPatientDeathProportion",
                                e
                              );
                            }}
                            error={
                              summaryData?.malariaInPatientDeathProportion ==
                                null &&
                              errors["malariaInPatientDeathProportion"] &&
                              errors["malariaInPatientDeathProportion"]
                            }
                            helperText={
                              summaryData?.malariaInPatientDeathProportion ==
                                null &&
                              errors["malariaInPatientDeathProportion"] &&
                              errors["malariaInPatientDeathProportion"]
                            }
                          />
                        </TableCell>

                        <TableCell>{}</TableCell>
                      </>
                    </TableRow>
                    <TableRow>
                      <>
                        <TableCell>
                          <i>
                            4.{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.TestPositivityRate"
                            )}
                          </i>
                        </TableCell>

                        <TableCell>
                          <RadioButtonGroup
                            id='testPositivityRate'
                            name='testPositivityRate'
                            row
                            color='primary'
                            options={[
                              new MultiSelectModel(
                                ConsistentTrendValue.Yes,
                                t("indicators-responses:Common:Yes")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.No,
                                t("indicators-responses:Common:No")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.NA,
                                t("indicators-responses:Common:NA")
                              ),
                            ]}
                            value={ConsistentTrendHelper.fromBoolean(
                              summaryData?.testPositivityRate
                            )}
                            defaultValue={bindConsistencyOverTimeForCoreIndicators(
                              summaryData?.testPositivityRate,
                              "testPositivityRate"
                            )}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetRadioChange(
                                "testPositivityRate",
                                e
                              );
                            }}
                            error={
                              summaryData?.testPositivityRate == null &&
                              errors["testPositivityRate"] &&
                              errors["testPositivityRate"]
                            }
                            helperText={
                              summaryData?.testPositivityRate == null &&
                              errors["testPositivityRate"] &&
                              errors["testPositivityRate"]
                            }
                          />
                        </TableCell>
                        <TableCell>{}</TableCell>
                      </>
                    </TableRow>
                    <TableRow>
                      <>
                        <TableCell>
                          <i>
                            5.{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.SidePositivityRate"
                            )}
                          </i>
                        </TableCell>

                        <TableCell>
                          <RadioButtonGroup
                            id='slidePositivityRate'
                            name='slidePositivityRate'
                            row
                            color='primary'
                            options={[
                              new MultiSelectModel(
                                ConsistentTrendValue.Yes,
                                t("indicators-responses:Common:Yes")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.No,
                                t("indicators-responses:Common:No")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.NA,
                                t("indicators-responses:Common:NA")
                              ),
                            ]}
                            value={ConsistentTrendHelper.fromBoolean(
                              summaryData?.slidePositivityRate
                            )}
                            defaultValue={bindConsistencyOverTimeForCoreIndicators(
                              summaryData?.slidePositivityRate,
                              "slidePositivityRate"
                            )}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetRadioChange(
                                "slidePositivityRate",
                                e
                              );
                            }}
                            error={
                              summaryData?.slidePositivityRate == null &&
                              errors["slidePositivityRate"] &&
                              errors["slidePositivityRate"]
                            }
                            helperText={
                              summaryData?.slidePositivityRate == null &&
                              errors["slidePositivityRate"] &&
                              errors["slidePositivityRate"]
                            }
                          />
                        </TableCell>

                        <TableCell>{}</TableCell>
                      </>
                    </TableRow>
                    <TableRow>
                      <>
                        <TableCell>
                          <i>
                            6.{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.RDTPositivityRate"
                            )}
                          </i>
                        </TableCell>

                        <TableCell>
                          <RadioButtonGroup
                            id='rdtPositivityRate'
                            name='rdtPositivityRate'
                            row
                            color='primary'
                            options={[
                              new MultiSelectModel(
                                ConsistentTrendValue.Yes,
                                t("indicators-responses:Common:Yes")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.No,
                                t("indicators-responses:Common:No")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.NA,
                                t("indicators-responses:Common:NA")
                              ),
                            ]}
                            value={ConsistentTrendHelper.fromBoolean(
                              summaryData?.rdtPositivityRate
                            )}
                            defaultValue={bindConsistencyOverTimeForCoreIndicators(
                              summaryData?.rdtPositivityRate,
                              "rdtPositivityRate"
                            )}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetRadioChange(
                                "rdtPositivityRate",
                                e
                              );
                            }}
                            error={
                              summaryData?.rdtPositivityRate == null &&
                              errors["rdtPositivityRate"] &&
                              errors["rdtPositivityRate"]
                            }
                            helperText={
                              summaryData?.rdtPositivityRate == null &&
                              errors["rdtPositivityRate"] &&
                              errors["rdtPositivityRate"]
                            }
                          />
                        </TableCell>

                        <TableCell>{}</TableCell>
                      </>
                    </TableRow>
                    <TableRow>
                      <>
                        <TableCell>
                          <i>
                            7.{" "}
                            {t(
                              "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.suspectedTests"
                            )}
                          </i>
                        </TableCell>

                        <TableCell>
                          <RadioButtonGroup
                            id='suspectedTestProportion'
                            name='suspectedTestProportion'
                            row
                            color='primary'
                            options={[
                              new MultiSelectModel(
                                ConsistentTrendValue.Yes,
                                t("indicators-responses:Common:Yes")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.No,
                                t("indicators-responses:Common:No")
                              ),
                              new MultiSelectModel(
                                ConsistentTrendValue.NA,
                                t("indicators-responses:Common:NA")
                              ),
                            ]}
                            value={ConsistentTrendHelper.fromBoolean(
                              summaryData?.suspectedTestProportion
                            )}
                            defaultValue={bindConsistencyOverTimeForCoreIndicators(
                              summaryData?.suspectedTestProportion,
                              "suspectedTestProportion"
                            )}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              nationalLevelTargetRadioChange(
                                "suspectedTestProportion",
                                e
                              );
                            }}
                            error={
                              summaryData?.suspectedTestProportion == null &&
                              errors["suspectedTestProportion"] &&
                              errors["suspectedTestProportion"]
                            }
                            helperText={
                              summaryData?.suspectedTestProportion == null &&
                              errors["suspectedTestProportion"] &&
                              errors["suspectedTestProportion"]
                            }
                          />
                        </TableCell>

                        <TableCell>{}</TableCell>
                      </>
                    </TableRow>
                  </>
                </TableBody>
              ) : (
                <TableRow>
                  <>
                    <TableCell></TableCell>
                    <TableCell>
                      <>
                        <div className='d-flex h-200 p-3 align-items-center'>
                          {t(
                            "Assessment.DataCollection.DataQualityAssessment.NoRecordsAvailable"
                          )}
                        </div>
                      </>
                    </TableCell>
                    <TableCell></TableCell>
                  </>
                </TableRow>
              )}
            </>
          </Table>

          {serviceLevelData.reportCompleteness !== null ? (
            <div className='col-xs-12 col-md-6'>
              <div className='mt-3'>
                <p className='fw-bold'>
                  <span>
                    1.2.14 &nbsp;
                    {t(
                      "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.dataQualityReason"
                    )}
                  </span>
                </p>
                <TextBox
                  id='dataQualityResultReason'
                  name='dataQualityResultReason'
                  placeholder={t(
                    "Assessment.DataCollection.DataQualityAssessment.nationalLevelSummary.dataQualityReasonText"
                  )}
                  rows={6}
                  multiline
                  fullWidth
                  value={summaryData?.dataQualityResultReason}
                  onChange={(evt: React.ChangeEvent<HTMLInputElement>) => {
                    dataQualityResultReasonChange(
                      "dataQualityResultReason",
                      evt
                    );
                  }}
                  maxLength={2000}
                  error={
                    errors["dataQualityResultReason"] &&
                    errors["dataQualityResultReason"]
                  }
                  helperText={
                    errors["dataQualityResultReason"] &&
                    errors["dataQualityResultReason"]
                  }
                />
              </div>
            </div>
          ) : (
            <></>
          )}
          {serviceLevelData.reportCompleteness !== null ? (
            <div className='col-xs-12 col-md-12 mt-3'>
              <p className='mb-0'>
                {parse(
                  t(
                    "Assessment.DataCollection.DataQualityAssessment.LegendTextCoreVariables"
                  )
                )}
              </p>
            </div>
          ) : (
            <></>
          )}

          <div className='response-wrapper'>
            <div className='button-action-section d-flex justify-content-center py-3'>
              {canEditAndSaveDeskLevelDQASummaryResult &&
                serviceLevelData.reportCompleteness !== null &&
                !summaryData?.isFinalized && (
                  <Button
                    className={classNames("btn", "app-btn-secondary")}
                    onClick={onSave}
                  >
                    {t("Common.Save")}
                  </Button>
                )}
              {canFinalizeDeskLevelDQASummaryResult &&
                isDeskLevelSummaryDataSaved && (
                  <Button
                    className={classNames("btn", "app-btn-secondary")}
                    onClick={onFinalize}
                  >
                    {t("Common.Finalize")}
                  </Button>
                )}

              {summaryData?.isFinalized && canExportDeskLevelDQASummary && (
                <Button
                  className={classNames("btn", "app-btn-secondary")}
                  onClick={onGenerateTemplate}
                >
                  {t("Common.Export")}
                </Button>
              )}

              <DownloadInProgressModal isFileDownloading={isFileDownloading} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DeskLevelSummaryComponent;
