import React, { useEffect, useState } from 'react';
import { CompositeFilterDescriptor, SortDescriptor } from '@progress/kendo-data-query';
import {
  Grid,
  GridColumn as Column,
  GridColumnProps,
  GridHeaderCellProps,
  GridProps,
  GridSortChangeEvent,
  GridRowProps,
  GridCustomRowProps,
} from '@progress/kendo-react-grid';
import GridHeaderCell from './GridHeaderCell';
import { useDispatch, useSelector } from 'react-redux';
import { setFilterPanelOpen } from '../../redux/ducks/data-grid-filter-panel';



type DataGridProps = GridProps & {
  columns: Array<GridColumnProps>;
  className?: string;
  defaultHeight?: string;
  initialSort?: Array<SortDescriptor>;
  onSortChange?: (e: GridSortChangeEvent) => void;
  onFilterChange?: (e: any) => void;
  filterable?: boolean;
  hasActionBtn?: boolean;
  rowRender?: (
    trElement: React.ReactElement<HTMLTableRowElement>,
    rowProps: GridRowProps
  ) => React.ReactElement;
};

const DataGrid = (props: DataGridProps) => {
  const {
    columns,
    defaultHeight,
    data,
    total,
    pageable,
    hasActionBtn,
    rowRender,
    take,
    skip,
    onPageChange,
    onFilterChange,
    ...restProps
  } = props;

  const [filter, setFilter] = useState<CompositeFilterDescriptor | null>(null);
  const dispatch = useDispatch();
  const openFilter = useSelector((state: any) => state.filterPanel)

  const onFilterIconClick = () => {
    dispatch(setFilterPanelOpen(true))
  };
  const onCloseClick = () => {
    dispatch(setFilterPanelOpen(false))
    setFilter(null); 
    if (onFilterChange) {
      onFilterChange({ filter: null });
    }
  };

  const cells = {
    headerCell: (headerCellProps: GridHeaderCellProps) => (
      <GridHeaderCell
        open={openFilter}
        data={data}
        hasActionBtn={hasActionBtn}
        onFilterIconClick={onFilterIconClick}
        onCloseClick={onCloseClick}
        {...headerCellProps}
        {...props}
      />
    ),
  };

  const rows = rowRender
    ? {
        data: (rowProps: GridCustomRowProps) => {
          const defaultTr = <tr {...rowProps.trProps}>{rowProps.children}</tr>;
          return rowRender(defaultTr, rowProps);
        },
      }
    : undefined;

  return (
      <Grid
        style={{ height: defaultHeight || 'auto' }}
        filterable={openFilter}
        filter={filter ?? undefined}
        pageable={pageable ? { buttonCount: 4, pageSizes: false, type: 'numeric'} : undefined}
        total={total}
        cells={cells}
        rows={rows}
        data={data} 
        take={take}
        skip={skip}
        onPageChange={onPageChange}
        onFilterChange={(e) => {
        setFilter(e.filter); // Grid refresh won't affect openFilter
          if (onFilterChange) {
            onFilterChange(e);
          }
        }}
        {...restProps}
        autoProcessData={false}
        sortable={true}
        >
            {columns.map((columnProps: GridColumnProps, index: number) => (
                <Column key={`${columnProps.field}_${index}`} {...columnProps} locked={columnProps.locked ? true : false} />
            ))}
        </Grid>
    );
};

export default DataGrid;

