﻿import React from "react";
import { DatePicker as MuiDatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { TextField } from "@mui/material";
import frLocale from "date-fns/locale/fr";
import enLocale from "date-fns/locale/en-US";

import { Constants } from "../../models/Constants";
import { UtilityHelper } from "../../utils/UtilityHelper";

interface DatePickerProps {
  views?: string[];
  value?: Date | null;
  onAccept?: (date: Date | null) => void;
  format?: string;
  error?: boolean;
  helperText?: string;
  label?: string;
  disabled?: boolean;
  className?: string;
  onChange?: (date: Date | null) => void;
}

/** Render Date control */
const DatePicker = (props: DatePickerProps) => {
  const { format, error, helperText, ...otherProps } = props;  

  const currentlanguage = UtilityHelper.getCookieValue("i18next");

  let localeLanguage: any;
  if (currentlanguage === "fr") localeLanguage = frLocale;
  else if (currentlanguage === "en") {
    localeLanguage = enLocale;
  }

  return (
    <LocalizationProvider
      dateAdapter={AdapterDateFns}
      adapterLocale={localeLanguage}
    >
      <MuiDatePicker
        format={format || Constants.Common.DefaultDateFormat}
        {...otherProps}
        slots={{
          textField: TextField,
        }}
        slotProps={{
          textField: {
            error: error,
            helperText: helperText,
            variant: "outlined",
            margin: "normal",
            className: "form-control inputfocus",
          },
        }}
      />
    </LocalizationProvider>
  );
};

export default DatePicker;
