import React, { useEffect } from "react";
import { GridHeaderCellProps } from "@progress/kendo-react-grid";
import FilterListIcon from "@mui/icons-material/FilterList";
import CloseIcon from "@mui/icons-material/Close";
import { IconButton } from "@mui/material";

type GridHeaderProps = GridHeaderCellProps & {
  open: boolean;
  data?: any;
  // onFilterClick: (evt: React.MouseEvent<HTMLSpanElement>) => void;
  hasActionBtn?: boolean;
  field?: string,
  onFilterIconClick: () => void;
  onCloseClick: () => void;
};
/** Renders the Grid Header Cell  */
function GridHeaderCell(props: GridHeaderProps) {

  const { open, data, field, hasActionBtn,onFilterIconClick,onCloseClick } = props;

  const canShowFilterIcon = true;
 
  return canShowFilterIcon ? (
    <th>
    <a className="k-link">
      <span className="k-head-text" title={props.title} onClick={props.onClick}> {props.title} </span>
      {
        <span className={hasActionBtn ? "grid-icon grid-icon-hide" : "grid-icon"}>
          {field &&(!open ? (
            <IconButton onClick={onFilterIconClick}>
              <FilterListIcon />
            </IconButton>
          ) : (
            <IconButton aria-label="Close" onClick={onCloseClick}>
              <CloseIcon />
            </IconButton>
          ))}
        </span>
      }
    </a>
    </th>
  ) : (
    <>
      <span title={props.title}> {props.title} </span>
    </>
  );
}

export default GridHeaderCell;