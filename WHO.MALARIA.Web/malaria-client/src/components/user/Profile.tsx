import React, { useEffect, useState } from "react";
import TextBox from "../controls/TextBox";
import {
  CurrentUserModel,
  UserCountryAccessModel,
  UserModel,
} from "../../models/ProfileModel";
import {
  CreateUserRequestModel,
  UpdateUserRequestModel,
} from "../../models/RequestModels/UserRequestModel";
import ModalFooter from "../controls/ModalFooter";
import MultiSelect from "../controls/MultiSelect";
import MultiSelectModel from "../../models/MultiSelectModel";
import { Constants } from "../../models/Constants";
import { getCall } from "../../services/api";
import { notificationService } from "../../services/notificationService";
import { BaseMessageModel, ErrorModel } from "../../models/ErrorModel";
import i18next from "../../i18n";
import { authService } from "../../services/authService";
import { userService } from "../../services/userService";
import { commonServices } from "../../services/commonService";
import { useTranslation } from "react-i18next";
import { UserRoleEnum, StatusCode } from "../../models/Enums";
import { UtilityHelper } from "../../utils/UtilityHelper";
import { Button, IconButton } from "@mui/material";
import InfoIcon from "@mui/icons-material/Info";
import Tooltip from "../controls/Tooltip";

interface IProfile {
  onCancel?: () => void;
  registerUser: boolean;
  isAccessRequestForCountryFromWhoUser?: boolean;
  setIsAccessRequestForCountryFromWhoUser?: (param: boolean) => void;
  updateUserDetails?: (param: UserModel) => void;
}

/** Renders the user profile page */
const Profile = (props: IProfile) => {
  const {
    onCancel,
    registerUser,
    setIsAccessRequestForCountryFromWhoUser,
    updateUserDetails,
    isAccessRequestForCountryFromWhoUser,
  } = props;
  const [countries, setCountries] = useState<Array<MultiSelectModel>>([]);

  const [user, setUser] = useState<UserModel>(UserModel.init());
  const userInfo: CurrentUserModel = authService.getCurrentUser();
  const [error, setError] = useState<any>({});

  const { t } = useTranslation();

  useEffect(() => {
    getCountries();
  }, []);

  useEffect(() => {
    if (registerUser && isAccessRequestForCountryFromWhoUser) {
      const userModel = new UserModel(
        "",
        userInfo.name,
        "",
        userInfo.email,
        ""
      );
      setUser(userModel);
    }
  }, [countries]);

  // get countries list based on grant access
  const getCountriesList = (
    userCountryAccess: Array<UserCountryAccessModel>,
    isAccessGranted: boolean
  ) => {
    return userCountryAccess?.filter((uca: UserCountryAccessModel) => {
      return uca?.isActive === isAccessGranted;
    });
  };

  // get countries ids with multiSelect chip
  const getCountryIds = (countryIds: Array<string>) => {
    return countries
      .filter((country: MultiSelectModel) => countryIds.includes(country.id))
      .map(
        (country: MultiSelectModel): MultiSelectModel =>
          new MultiSelectModel(country.id, country.text, true, false)
      );
  };

  // changing user info with country list to show in forms
  const getUserInfoWithCountries = (response: any) => {
    let user: UserModel = response?.user;
    user.inActivatedCountryIds = response?.inActivatedCountryIds || [];
    user.pendingRequestCountryIds = response?.pendingRequestCountryIds || [];

    // filtering granted countries list from user country total list - if isActive true
    const grantedAccessCountries = getCountriesList(
      response?.userCountryAccess,
      true
    );

    // filtering all pending country requests
    const requestAccessCountries = getCountriesList(
      response?.userCountryAccess,
      false
    );

    const grantedCountryIds: Array<string> = grantedAccessCountries?.map(
      (uca: UserCountryAccessModel) => uca.countryId
    );

    const requestedCountyIds: Array<string> = requestAccessCountries?.map(
      (uca: UserCountryAccessModel) => uca.countryId
    );

    // country request accessing ids
    user.countryRequestedForIds = getCountryIds(user.pendingRequestCountryIds);

    // country access granted ids
    user.accessRequestedCountryIds = getCountryIds(grantedCountryIds);
    user.accessGrantedCountryIds = grantedCountryIds;

    return user;
  };

  // get user by making api call
  const getUser = (id: string) => {
    getCall(`${Constants.Api.Url.GET_USER_PROFILE}/${id}`)
      .then(response => {
        if (!response || response.length == 0) return;
        const user = getUserInfoWithCountries(response);

        //Update user details if access request for country from who user
        if (isAccessRequestForCountryFromWhoUser) {
          updateUserDetails && updateUserDetails(user);
        }
        setUser(user);
      })
      .catch(error => {
        if (error) {
          notificationService.sendMessage(
            new BaseMessageModel(
              i18next.t("Errors.SomethingWentWrong"),
              StatusCode.PreConditionFailed
            )
          );
        }
      });
  };

  // retrieve all records from country entity by making an api call
  const getCountries = () => {
    // replace the placeholder by entity name
    commonServices.getCountries().then((records: any) => {
      setCountries(records);
    });
  };

  // triggers whenever user changes the control's value
  const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    setUser({
      ...user,
      [evt.target.name]: evt.target.value,
    });

    removeControlValidationOnChange(evt.target.value, evt.target.name);
  };

  // triggers whenever user add/removes items in multi-select dropdown
  const onMultiSelectChange = (
    field: string,
    selectedItems: Array<MultiSelectModel>
  ) => {
    setUser({ ...user, [field]: selectedItems });

    removeControlValidationOnChange(selectedItems, field);
  };

  // triggers on save
  const onSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // validate
    if (!isFormValid()) {
      setError(validate());
      return;
    }
    // filtering all new entered city ids
    const selectedCountryIds = user.countryRequestedForIds
      .filter((key: MultiSelectModel) => key.canRemoveChip)
      .map((uca: MultiSelectModel) => uca.id);

    if (isAccessRequestForCountryFromWhoUser && user.id) {
      //For access request for country from who user
      userService.countryAccessRequestOfWhoUser(
        new UpdateUserRequestModel(
          user.id,
          user.email,
          user.name,
          user.organizationName,
          selectedCountryIds
        )
      );
      setIsAccessRequestForCountryFromWhoUser &&
        setIsAccessRequestForCountryFromWhoUser(true);

      //Tracking access request for country event in analytics
      UtilityHelper.onEventAnalytics(
        "My Profile",
        "Country access request",
        "Country access request"
      );
    } else if (!user.id) {
      userService.register(
        new CreateUserRequestModel(
          user.email,
          user.name,
          user.organizationName,
          selectedCountryIds
        )
      );

      //Tracking register new user event in analytics
      UtilityHelper.onEventAnalytics("My Profile", "Register", "My Profile");
    } else if (user.id) {
      userService.updateProfile(
        new UpdateUserRequestModel(
          user.id,
          user.email,
          user.name,
          user.organizationName,
          selectedCountryIds
        )
      );

      //Tracking update profile event in analytics
      UtilityHelper.onEventAnalytics(
        "My Profile",
        "Update profile",
        "My Profile"
      );
    }

    onCancel && onCancel();
  };

  useEffect(() => {
    // wait until countries are bound and then  make getUser api call
    if (countries.length > 0 && userInfo.userId && !registerUser) {
      getUser(userInfo.userId);
    }
  }, [countries]);

  // to check email validation
  const validateEmail = (email: string) => {
    return Constants.Common.EmailRegx.test(String(email).toLowerCase());
  };

  // validate form
  const validate = () => {
    let error: any = {};

    const ignoreRequiredFields = [
      "id",
      "identityId",
      "userType",
      "country",
      "comment",
      "userCountryAccessId",
      "countryId",
      "accessRequestedCountryIds",
      "isWhoAdmin",
      "inActivatedCountryIds",
      "accessGrantedCountryIds",
      "pendingRequestCountryIds",
    ];

    if (user?.id !== "") {
      ignoreRequiredFields.push("countryRequestedForIds");
    }

    Object.keys(user)
      .filter((key: string) => !ignoreRequiredFields.includes(key))
      .forEach((key: string) => {
        const value = (user as any)[key];

        if (key === "email") {
          if (
            typeof value === "string" &&
            value.length > 0 &&
            !validateEmail(value)
          ) {
            error[key] = t("Errors.InvalidEmail");
          }
          return;
        }

        const isEmpty =
          value === null ||
          value === undefined ||
          (typeof value === "string" && value.trim() === "") ||
          (Array.isArray(value) && value.length === 0);

        if (isEmpty) {
          error[key] = t("Errors.MandatoryField");
        }
      });

    return error;
  };

  // checks and remove validation for the control if satisfies the validate condition
  const removeControlValidationOnChange = (
    value: string | Array<MultiSelectModel>,
    field: string
  ) => {
    if (value?.length > 0) {
      const formError = { ...error };
      delete formError[field];
      setError({ ...formError });
    } else {
      setError({ ...error, [field]: t("Errors.MandatoryField") });
    }
  };
  const isFormValid = () => Object.keys(validate()).length === 0;

  return (
    <form autoComplete='off' onSubmit={onSubmit}>
      <div className='row'>
        <div className='col-md-12 ml-auto'>
          <h6 className='blue-modal-text'>Hello, {user?.name || "User"}</h6>
          <p className='mb-0'>{t("GreetingMessage")} </p>
          <p className='mb-0'>
            {registerUser && t("UserManagement.RegisterGreetingInstruction")}
            <IconButton className='grid-icon-button'>
              <Tooltip
                content={t("UserManagement.RegisterGreetingNote")}
                isHtml
              >
                <InfoIcon fontSize='small' />
              </Tooltip>
            </IconButton>
          </p>
        </div>
        <div className='row mt-2'>
          <div className='col-md-12'>
            <TextBox
              id='name'
              name='name'
              value={user.name}
              label={t("UserManagement.GridColumn.Name")}
              error={!!error["name"]}
              helperText={error["name"]}
              autoComplete='off'
              fullWidth
              onChange={onChange}
              className='inputfocus'
              InputLabelProps={{ required: true }}
              maxLength={256}
              placeholder='Enter your full name'
            />
          </div>
        </div>

        <div className='row mt-3'>
          <div className='col-md-12'>
            <TextBox
              id='email'
              name='email'
              label={t("UserManagement.GridColumn.Email")}
              error={!!error["email"]}
              helperText={error["email"]}
              value={user.email}
              disabled={!registerUser && !isAccessRequestForCountryFromWhoUser} //It will be disabled for register user and access request for country from who-user
              autoComplete='off'
              fullWidth
              onChange={onChange}
              className='inputfocus'
              InputLabelProps={{ required: true }}
              maxLength={256}
            />
          </div>
        </div>

        <div className='row  mt-3'>
          <div className='col-md-12'>
            <TextBox
              id='organizationName'
              name='organizationName'
              label={t("UserManagement.GridColumn.OrganizationName")}
              error={!!error["organizationName"]}
              helperText={error["organizationName"]}
              value={user.organizationName}
              autoComplete='off'
              fullWidth
              onChange={onChange}
              className='inputfocus'
              InputLabelProps={{ required: true }}
              maxLength={256}
              disabled={user.userType == UserRoleEnum.WHOAdmin.toString()}
            />
          </div>
        </div>

        {!registerUser &&
        !isAccessRequestForCountryFromWhoUser &&
        user.userType != UserRoleEnum.WHOAdmin.toString() ? (
          <div className='row mt-3'>
            <div className='col-md-12'>
              <MultiSelect
                id='grantedCountries'
                label={t("UserManagement.CountryAccessGranted")}
                error={!!error["accessRequestedCountryIds"]}
                helperText={error["accessRequestedCountryIds"]}
                options={countries}
                disabled={true}
                values={user.accessRequestedCountryIds}
                placeholder='Countries Granted'
                onUpdate={(selectedItems: Array<MultiSelectModel>) =>
                  onMultiSelectChange(
                    "accessRequestedCountryIds",
                    selectedItems
                  )
                }
              />
            </div>
          </div>
        ) : (
          <></>
        )}
        {/* Show country access request to only when user click on register or user role is viewer or Who-user and clicks on my profile*/}
        {/* userInfo.userType === 0 for who-user */}
        {user && user.userType != UserRoleEnum.WHOAdmin.toString() ? (
          <div className='row mt-3'>
            <div className='col-md-12'>
              <MultiSelect
                id='requestedCountries'
                label={t("UserManagement.CountryAccessRequired")}
                error={!!error["countryRequestedForIds"]}
                helperText={error["countryRequestedForIds"]}
                options={countries.filter(
                  x =>
                    ![
                      ...user.accessGrantedCountryIds,
                      ...user.inActivatedCountryIds,
                      ...user.pendingRequestCountryIds,
                    ].includes(x.id)
                )}
                values={user.countryRequestedForIds}
                placeholder='Countries'
                onUpdate={(selectedItems: Array<MultiSelectModel>) =>
                  onMultiSelectChange("countryRequestedForIds", selectedItems)
                }
                required={registerUser}
              />
              <small>{t("UserManagement.CountryAccessTooltip")}</small>
            </div>
          </div>
        ) : (
          <></>
        )}
        <ModalFooter>
          <button className='btn app-btn-primary' type='submit'>
            {registerUser
              ? t("UserManagement.Register")
              : isAccessRequestForCountryFromWhoUser
                ? t("Common.Assign")
                : t("Common.Update")}
          </button>
        </ModalFooter>
      </div>
    </form>
  );
};

export default Profile;
