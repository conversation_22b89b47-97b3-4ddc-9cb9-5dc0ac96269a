import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { AnimatePresence } from "framer-motion";
import Header from "../../components/ui/Header";
import Controls from "../../components/user/Controls";
import DashboardContainer from "../user/DashboardContainer";
import AssessmentContainer from "../assessments/AssessmentContainer";
import NotFound from "../common/NotFound";
import UnAuthorizedAccess from "../common/UnAuthorizedAccess";
import UserContainer from "../user/UserContainer";
import Assessments from "../assessments/Assessments";
import { UtilityHelper } from "../../utils/UtilityHelper";
import {
  AssessmentTabs,
  DataCollectionStepper,
  ReportGenerationStepper,
  StrategyStepper,
  UserRoleEnum,
} from "../../models/Enums";
import React from "react";
import IndicatorResponseContainer from "../assessments/data-collection/desk-review/responses/IndicatorResponseContainer";
import UploadObjective_3_DiagramContainer from "../assessments/data-collection/desk-review/diagrams/objective_3/UploadObjective_3_DiagramContainer";
import UploadSubObjective_2_2_DiagramContainer from "../assessments/data-collection/desk-review/diagrams/objective_2/UploadSubObjective_2_2_DiagramContainer";
import ConfigureSurvey from "../assessments/data-collection/question-bank/ConfigureSurvey";
import CountryAccessRequestForWhoUser from "../user/CountryAccessRequestForWhoUser";
import Analytics from "../common/Analytics";
import AnalyticalOutputContainer from "../assessments/data-analysis/desk-review/AnalyticalOutputContainer";
import AnalyticalOutputDiagramContainer from "../assessments/data-analysis/desk-review/AnalyticalOutputDiagramContainer";
import { Constants } from "../../models/Constants";
import HealthFacilities from "../assessments/health-facilities/HealthFacilities";
import IndicatorReportContainer from "../assessments/data-analysis/data-quality-assessment/desk-level-delivery/IndicatorReportContainer";
import DeskLevelSummaryComponent from "../assessments/data-analysis/data-quality-assessment/desk-level-delivery/DeskLevelSummaryComponent";
import UploadDocument from "../user/UploadDocument";
import DeactivatedUser from "../common/DeactivatedUser";
import InActivatedUser from "../common/InActivatedUser";
import DeActivatedWhoUser from "../common/DeActivatedWhoUser";
import { Navigate } from "react-router-dom";

// Wrapper component that provides the required props to AssessmentContainer
const AssessmentContainerWrapper = () => {
  const location = useLocation();
  const currentTabIndex = getCurrentTabIndex({ location });
  const currentStepIndex = getCurrentStepIndex({ location });
  const country = location?.state?.country || "";

  return (
    <AssessmentContainer
      country={country}
      currentTabIndex={currentTabIndex}
      currentStepIndex={currentStepIndex}
    />
  );
};

function PrivateRoutes() {
  return (
    <BrowserRouter>
      <Header />
      <AnimatePresence>
        <main className='app-main'>
          <Routes>
            <Route path='/' element={<Navigate to='/dashboard' replace />} />
            <Route path='/dashboard' element={<DashboardContainer />} />

            <Route path='/controls' element={<Controls />} />

            <Route
              path='/super-managers'
              element={authorizedComponent(
                [UserRoleEnum.WHOAdmin],
                <UserContainer userType={UserRoleEnum.WHOAdmin} />
              )}
            />

            <Route
              path='/managers'
              element={authorizedComponent(
                [UserRoleEnum.SuperManager],
                <UserContainer userType={UserRoleEnum.SuperManager} />
              )}
            />
            <Route
              path='/analytics'
              element={authorizedComponent(
                [UserRoleEnum.WHOAdmin],
                <Analytics />
              )}
            />
            <Route
              path='/health-facilities'
              element={authorizedComponent(
                [UserRoleEnum.Manager, UserRoleEnum.SuperManager],
                <HealthFacilities />
              )}
            />
            <Route
              path='/upload-document'
              element={authorizedComponent(
                [UserRoleEnum.WHOAdmin],
                <UploadDocument />
              )}
            />
            <Route
              path='/assessment/scope-definition/strategy'
              element={<AssessmentContainerWrapper />}
            />
            <Route
              path='/assessment/scope-definition/indicator'
              element={<AssessmentContainerWrapper />}
            />
            <Route
              path='/assessment/data-collection/desk-review'
              element={<AssessmentContainerWrapper />}
            />
            <Route
              path='/assessment/data-collection/dqa'
              element={<AssessmentContainerWrapper />}
            />
            <Route
              path='/assessment/data-collection/question-bank'
              element={<AssessmentContainerWrapper />}
            />
            <Route
              path='/assessment/data-collection/survey-result'
              element={<AssessmentContainerWrapper />}
            />
            <Route
              path='/assessment/data-analysis/desk-review'
              element={<AssessmentContainerWrapper />}
            />
            <Route
              path='/assessment/data-analysis/dqa'
              element={<AssessmentContainerWrapper />}
            />
            <Route
              path='/assessment/data-analysis/question-bank'
              element={<AssessmentContainerWrapper />}
            />
            <Route
              path='/assessment/report-generation/report'
              element={<AssessmentContainerWrapper />}
            />
            <Route
              path='/assessment/report-generation/score-card'
              element={<AssessmentContainerWrapper />}
            />
            <Route path='/assessments' element={<Assessments />} />

            <Route
              path='/assessment/data-collection/desk-review/indicator/response'
              element={<IndicatorResponseContainer />}
            />
            <Route
              path='/assessment/data-collection/dqa/indicator/response'
              element={<IndicatorResponseContainer />}
            />

            <Route
              path='/assessment/data-collection/desk-review/objective-3/upload-diagram'
              element={<UploadObjective_3_DiagramContainer />}
            />

            <Route
              path='/assessment/data-collection/desk-review/objective-2_2/upload-diagram'
              element={<UploadSubObjective_2_2_DiagramContainer />}
            />

            <Route
              path='/assessment/data-collection/question-bank/questions/configure'
              element={<ConfigureSurvey />}
            />

            <Route
              path='/assessment/data-analysis/desk-review/indicator/view'
              element={<AnalyticalOutputContainer />}
            />

            <Route
              path={Constants.Route.Url.ANALYTICAL_OUTPUT_UPLOADED_DIAGRAM}
              element={<AnalyticalOutputDiagramContainer />}
            />

            <Route
              path='/assessment/data-analysis/dqa/indicator/response'
              element={<IndicatorReportContainer />}
            />

            <Route
              path='/assessment/data-analysis/dqa/indicator/summary'
              element={<DeskLevelSummaryComponent />}
            />

            <Route
              path='/countryaccessrequest'
              element={<CountryAccessRequestForWhoUser />}
            />
            <Route path='/deactivateduser' element={<DeactivatedUser />} />
            <Route
              path='/deactivatedwhouser'
              element={<DeActivatedWhoUser />}
            />
            <Route path='/inActivatedUser' element={<InActivatedUser />} />
            <Route path='*' element={<NotFound />} />
          </Routes>
        </main>
      </AnimatePresence>
    </BrowserRouter>
  );
}

// get the current tab index based on the url
const getCurrentTabIndex = (props: any) => {
  const pathName = props.location.pathname;
  if (pathName.indexOf("scope-definition") > -1) {
    return AssessmentTabs.ScopeDefinition;
  } else if (pathName.indexOf("data-collection") > -1) {
    return AssessmentTabs.DataCollection;
  } else if (pathName.indexOf("data-analysis") > -1) {
    return AssessmentTabs.DataAnalysis;
  } else if (pathName.indexOf("report-generation") > -1) {
    return AssessmentTabs.ReportGeneration;
  }

  return -1; // un-matched
};

// get current current step index based on URL
const getCurrentStepIndex = (props: any) => {
  const pathName = props.location.pathname;
  if (pathName.indexOf("strategy") > -1) {
    return StrategyStepper.Strategy;
  } else if (pathName.indexOf("indicator") > -1) {
    return StrategyStepper.Indicator;
  } else if (pathName.indexOf("desk-review") > -1) {
    return DataCollectionStepper.DeskReview;
  } else if (pathName.indexOf("dqa") > -1) {
    return DataCollectionStepper.DQA;
  } else if (pathName.indexOf("question-bank") > -1) {
    return DataCollectionStepper.QuestionBank;
  } else if (pathName.indexOf("survey-result") > -1) {
    return DataCollectionStepper.SurveyResult;
  } else if (pathName.indexOf("score-card") > -1) {
    return ReportGenerationStepper.ScoreCard;
  } else if (pathName.indexOf("report") > -1) {
    return ReportGenerationStepper.Report;
  }

  return -1; // un-matched
};

/** Prohibit unauthorized user to access the component */
const authorizedComponent = (
  roles: Array<number>,
  Component: React.ReactElement
): any => {
  if (UtilityHelper.isInRole(roles)) {
    return Component;
  }
  return <UnAuthorizedAccess />;
};

export default PrivateRoutes;
