export class DeskLevelSummaryYearModel {
    constructor(
        public year: string | null,
        public assessmentId: string | null
    ) {
        this.year = year;
        this.assessmentId = assessmentId;
    }
    static init = () => new DeskLevelSummaryYearModel("", "");
}

export class SummaryDataModel {
    constructor(
        public assessmentId: string | null,
        public reportCompleteness: number | null,
        public reportTimeliness: number | null,
        public variableCompleteness: number | null,
        public variableConsistency: number | null,
        public variableConcordance: number | null,
        public malariaOutpatientProportion: boolean | null,
        public malariaInPatientProportion: boolean | null,
        public malariaInPatientDeathProportion: boolean | null,
        public testPositivityRate: boolean | null,
        public slidePositivityRate: boolean | null,
        public rdtPositivityRate: boolean | null,
        public suspectedTestProportion: boolean | null,
        public type: number | null,
        public year: number | null,
        public dataQualityResultReason: string | null,
        public isFinalized: boolean,
    ) {
        this.assessmentId = assessmentId;
        this.reportCompleteness = reportCompleteness;
        this.reportTimeliness = reportTimeliness;
        this.variableCompleteness = variableCompleteness;
        this.variableConsistency = variableConsistency;
        this.variableConcordance = variableConcordance;
        this.malariaOutpatientProportion = malariaOutpatientProportion;
        this.malariaInPatientProportion = malariaInPatientProportion;
        this.malariaInPatientDeathProportion = malariaInPatientDeathProportion;
        this.testPositivityRate = testPositivityRate;
        this.slidePositivityRate = slidePositivityRate;
        this.rdtPositivityRate = rdtPositivityRate;
        this.suspectedTestProportion = suspectedTestProportion;
        this.type = type;
        this.year = year;
        this.dataQualityResultReason = dataQualityResultReason;
        this.isFinalized = isFinalized;
    }

    static init = (assessmentId: string) => new SummaryDataModel(assessmentId, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "", false);

}

export class DeskLevelSummaryReportModel {
    constructor(
        public summaryData: SummaryDataModel,
        public currentUserId: string
    ) {
        this.summaryData = summaryData;
        this.currentUserId = currentUserId;
    }
}

