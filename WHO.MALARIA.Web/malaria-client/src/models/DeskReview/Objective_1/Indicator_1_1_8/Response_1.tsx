export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public strategyId: string | null,
        public hasTESBeenCarriedOut: boolean | null,
        public noOfSentinelSites: number | null,
        public year: number | null,
        public summaryDetails: string | null,
        public treatmentFailure: Array<TreatmentFailure>
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.strategyId = strategyId;
        this.hasTESBeenCarriedOut = hasTESBeenCarriedOut;
        this.noOfSentinelSites = noOfSentinelSites;
        this.year = year;
        this.summaryDetails = summaryDetails;
        this.treatmentFailure = treatmentFailure;
    }
    static init = (strategyId: string) =>
        new Response_1(false, null, strategyId, true, null, null, null, [
            new TreatmentFailure(null, null, null, null),
        ]);
}

export class TreatmentFailure {
    constructor(
        public drugName: string | null,
        public treatmentFailureAtDay28Percent: number | null,
        public treatmentFailureAtDay42Percent: number | null,
        public patientsPositiveOnDay3Percent: number | null
    ) {
        this.drugName = drugName;
        this.treatmentFailureAtDay28Percent = treatmentFailureAtDay28Percent;
        this.treatmentFailureAtDay42Percent = treatmentFailureAtDay42Percent;
        this.patientsPositiveOnDay3Percent = patientsPositiveOnDay3Percent;
    }
    static init = () => new TreatmentFailure(null, null, null, null);
}
