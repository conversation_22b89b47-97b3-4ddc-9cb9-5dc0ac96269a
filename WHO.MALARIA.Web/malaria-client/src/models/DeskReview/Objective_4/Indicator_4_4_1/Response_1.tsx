export class Response_1 {
    constructor(
        public cannotBeAssessed: boolean | false,
        public cannotBeAssessedReason: string | null,
        public metNotMetStatus: string | null,
        public hasTraining: boolean | null,
        public dataCollection: MalariaSurveillanceTraining,
        public dataReporting: MalariaSurveillanceTraining,
        public conductingDataQualityReview: MalariaSurveillanceTraining,
        public conductingDataAnalysis: MalariaSurveillanceTraining,
        public preparingDisseminationReports: MalariaSurveillanceTraining,
        public supervision: MalariaSurveillanceTraining,
        public caseNotification: MalariaSurveillanceTraining,
        public caseInvestigation: MalariaSurveillanceTraining,
        public caseClassification: MalariaSurveillanceTraining,
        public fociInvestigation: MalariaSurveillanceTraining,
        public fociClassification: MalariaSurveillanceTraining,
        public qualityAssuranceOfLabData: MalariaSurveillanceTraining,
        public trainingForMicroscopy: MalariaSurveillanceTraining,
        public trainingInPublicPrivateSectors: MalariaSurveillanceTraining,
        public attendants: TrainingDetails,
        public frequencyOfTraining: TrainingDetails,
        public lastDateOfTraining: TrainingDateDetails,
        public plannedTraining: TrainingDetail,
        public previousYearTraining: TrainingDetail,
        //National training proportion percentage is used to calculate the percentage of proportion for number of trainings/ number of planned trainings in the previous year and based on this field FE validation are added in validation rules
        //not saved in DB        
        public nationalTrainingProportion: number | null,
        //Subnational training proportion percentage is used to calculate the percentage of proportion for number of trainings/ number of planned trainings in the previous year and based on this field FE validation are added in validation rules
        //not saved in DB
        public subNationalTrainingProportion: number | null,
        //Service delivery training proportion percentage is used to calculate the percentage of proportion for number of trainings/ number of planned trainings in the previous year and based on this field FE validation are added in validation rules
        //not saved in DB
        public serviceDeliveryTrainingProportion: number | null
    ) {
        this.cannotBeAssessed = cannotBeAssessed;
        this.cannotBeAssessedReason = cannotBeAssessedReason;
        this.metNotMetStatus = metNotMetStatus;
        this.hasTraining = hasTraining;
        this.dataCollection = dataCollection;
        this.dataReporting = dataReporting;
        this.conductingDataQualityReview = conductingDataQualityReview;
        this.conductingDataAnalysis = conductingDataAnalysis;
        this.preparingDisseminationReports = preparingDisseminationReports;
        this.supervision = supervision;
        this.caseNotification = caseNotification;
        this.caseInvestigation = caseInvestigation;
        this.caseClassification = caseClassification;
        this.fociInvestigation = fociInvestigation;
        this.fociClassification = fociClassification;
        this.qualityAssuranceOfLabData = qualityAssuranceOfLabData;
        this.trainingForMicroscopy = trainingForMicroscopy;
        this.trainingInPublicPrivateSectors = trainingInPublicPrivateSectors;
        this.attendants = attendants;
        this.frequencyOfTraining = frequencyOfTraining;
        this.lastDateOfTraining = lastDateOfTraining;
        this.plannedTraining = plannedTraining;
        this.previousYearTraining = previousYearTraining;
        this.nationalTrainingProportion = nationalTrainingProportion;
        this.subNationalTrainingProportion = subNationalTrainingProportion;
        this.serviceDeliveryTrainingProportion = serviceDeliveryTrainingProportion;
    }

    static init = () =>
        new Response_1(
            false,
            null,
            null,
            true,
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new MalariaSurveillanceTraining(),
            new TrainingDetails(),
            new TrainingDetails(),
            new TrainingDateDetails(),
            new TrainingDetail(null, null, null),
            new TrainingDetail(null, null, null),
            null,
            null,
            null
        );
}

export class MalariaSurveillanceTraining {
    constructor(
        public national: boolean | null = null,
        public subNational: boolean | null = null,
        public serviceDeliveryLevel: boolean | null = null
    ) {
        this.national = national;
        this.subNational = subNational;
        this.serviceDeliveryLevel = serviceDeliveryLevel;
    }
}
export class TrainingDetails {
    constructor(
        public national: string | null = null,
        public subNational: string | null = null,
        public serviceDeliveryLevel: string | null = null
    ) {
        this.national = national;
        this.subNational = subNational;
        this.serviceDeliveryLevel = serviceDeliveryLevel;
    }
}
export class TrainingDetail {
    constructor(
        public national: number | null,
        public subNational: number | null,
        public serviceDeliveryLevel: number | null
    ) {
        this.national = national;
        this.subNational = subNational;
        this.serviceDeliveryLevel = serviceDeliveryLevel;
    }
}

export class TrainingDateDetails {
    constructor(
        public national: Date | null = null,
        public subNational: Date | null = null,
        public serviceDeliveryLevel: Date | null = null
    ) {
        this.national = new Date();
        this.subNational = new Date();
        this.serviceDeliveryLevel = new Date();
    }

}
