import { AssessmentTabs } from "../../models/Enums";

// action
export const SET_FILTER_PANEL_OPEN: string = "SET_FILTER_PANEL_OPEN";

/**Set assessment tab index
 * @param tabIndex assessment tab index
 */
export const setFilterPanelOpen= (panelState: boolean) => ({
    type: SET_FILTER_PANEL_OPEN,
    panelState,
});

export default (state: boolean = false, action: any) => {
    switch (action.type) {
        case SET_FILTER_PANEL_OPEN:
            const { panelState } = action;
            return panelState;

        default:
            return state;
    }
};