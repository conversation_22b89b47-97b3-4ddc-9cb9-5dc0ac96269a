﻿using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.Handlers.Queries;

namespace WHO.MALARIA.Web.Apis
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AssessmentQuestionBankController : BaseApiController
    {
        private readonly IAssessmentQueries _assessmentQueries;
        private readonly IAssessmentQuestionQueries _assessmentQuestionQueries;
        private readonly ITranslationService _translationService;

        public AssessmentQuestionBankController(IMediator mediator, IHttpContextAccessor httpContextAccessor, IAssessmentQueries assessmentQueries, IAssessmentQuestionQueries assessmentQuestionQueries, ITranslationService translationService) : base(mediator, httpContextAccessor)
        {
            _assessmentQueries = assessmentQueries;
            _assessmentQuestionQueries = assessmentQuestionQueries;
            _translationService = translationService;
        }

        /// <summary>
        /// Gets respondent types selected for the specific assessment
        /// </summary>
        /// <param name="assessmentId">Assessment id</param>
        /// <returns>List of respondent types for an assessment</returns>
        [HttpGet]
        [Route("{assessmentId}/respondentTypes")]
        public async Task<ActionResult<QBAssessmentRespondentTypeDto>> GetRespondentTypesAsync(Guid assessmentId)
        {
            return Ok(await _assessmentQueries.GetRespondentTypesAsync(assessmentId));
        }

        /// <summary>
        /// Saves respondent types data for assessment.
        /// </summary>
        /// <param name="command">Object of CreateQBAssessementRespondentTypeCommand class</param>
        /// <returns>True if save data of respondent types is successful</returns>
        [HttpPost]
        [Route("saveRespondentTypes")]
        public async Task<ActionResult<bool>> SaveRespondentTypesAsync([FromBody] CreateQBAssessementRespondentTypeCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Gets questions selected for the specific assessment
        /// </summary>
        /// <param name="assessmentId">Assessment id</param>
        /// <returns>List of questions for an assessment</returns>
        [HttpGet]
        [Route("{assessmentId}/questions")]
        public async Task<ActionResult<AssessmentQuestionDto>> GetQuestionsAsync(Guid assessmentId)
        {
            return Ok(await _assessmentQuestionQueries.GetAsync(assessmentId));
        }

        /// <summary>
        /// Save questions that are selected for an assessment
        /// </summary>
        /// <param name="command">Object of CreateQBAssessmentQuestionsCommand class</param>
        /// <returns>True if data saved successfully else 500 status code or validation message if any</returns>
        [HttpPost]
        [Route("saveQuestions")]
        public async Task<ActionResult<bool>> SaveQuestionsAsync([FromBody] CreateQBAssessmentQuestionsCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Finalize questionnaire associated with the assessment.
        /// </summary>
        /// <param name="command">Object of FinalizeQuestionnaireCommand containing properties required to finalize a questionnaire</param>
        /// <returns>True if questionnaire is finalized else false or validation message if any</returns>
        [HttpPut]
        [Route("finalize-questionnaire")]
        public async Task<ActionResult<bool>> FinalizeRespondentType([FromBody] FinalizeQuestionnaireCommand command)
        {
            command.CurrentUserId = GetCurrentUser().UserId;

            return Ok(await CommandAsync(command));
        }

        /// <summary>
        /// Get collection of indicators, sub-objective, objective information that are associated with the question bank and assessment 
        /// </summary>
        /// <param name="assessmentId">Assessment id</param>
        /// <returns>Collection of question bank indicators, sub-objectives and objectives</returns>
        [HttpGet]
        [Route("{assessmentId}/objectives/sub-objectives/indicators")]
        public async Task<ActionResult<ObjectivesSubObjectivesIndicatorsDto>> GetObjectiveSubObjectivesIndicatorsAsync(Guid assessmentId)
        {
            return Ok(await _assessmentQuestionQueries.GetObjectivesSubObjectivesIndicatorsAsync(assessmentId));
        }

        /// <summary>
        /// Generate questionnaires and shell table template
        /// </summary>       
        /// <param name="assessmentId">Assessment id for which question bank questions are to be fetched </param>
        /// <returns>ZIP file that contains questionnaire and shell table template</returns>
        [HttpGet]
        [Route("questionaire/generate/{assessmentId}")]
        public async Task<FileContentResult> GenerateQuestionnairesAsync(Guid assessmentId)
        {
            Guid currentUserId = GetCurrentUser().UserId;

            List<FileResponseDto> fileResponseDto = await _assessmentQuestionQueries.GenerateQuestionnairesAsync(currentUserId, assessmentId);

            byte[] memoryStream = UtilityHelper.CreateZIPFile(fileResponseDto);

            return File(memoryStream, Constants.DownloadDocument.ContentType, Constants.DownloadDocument.QuestionBank);
        }

        /// <summary>
        /// Check has health facility data
        /// </summary>      
        /// <param name="assessmentId">Assessment id for which health facility data are to be fetched</param> 
        /// <returns>Return true or false</returns>
        [HttpGet]
        [Route("hasHealthFacilityData/{assessmentId}")]
        public async Task<ActionResult> HasHealthFacilityDataAsync(Guid assessmentId)
        {
            Guid currentUserId = GetCurrentUser().UserId;

            return Ok(await _assessmentQuestionQueries.HasHealthFacilityDataAsync(currentUserId, assessmentId));
        }

        /// <summary>
        /// Upload health facilities
        /// </summary>
        /// <param name="command">UploadHealthFacilityDataCommand object</param>
        /// <returns>Returns true if health facility data is uploaded successfully else false</returns>
        [HttpPost]
        [Route("healthfacility/upload")]
        public async Task<ActionResult<bool>> UploadHealthFacilitiesAsync([FromForm] UploadHealthFacilitiesCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);
            return Ok(isSuccess);
        }

        /// <summary>
        /// Get last uploaded health facility filename
        /// </summary>
        /// <param name="countryId">Country Id</param>
        /// <returns>Returns health facility uploaded filename </returns>
        [HttpGet]
        [Route("healthfacility/Filename/{countryId}")]
        public ActionResult<string> GetUploadedHealthFacilitiesFileNameAsync(Guid countryId)
        {
            return Ok(_assessmentQuestionQueries.GetLatestHealthFacilitiesUploadedFilenameAsync(countryId));
        }

        /// <summary>
        /// Upload shell table
        /// </summary>
        /// <param name="command">UploadShellTableCommand object</param>
        /// <returns>Returns true if shell table data is uploaded successfully else false</returns>
        [HttpPost]
        [Route("shelltable/upload")]
        public async Task<ActionResult<bool>> UploadShellTableAsync([FromForm] UploadShellTableCommand command)
        {
            command.CurrentUserId = base.GetCurrentUser().UserId;
            bool isSuccess = await CommandAsync(command);
            return Ok(isSuccess);
        }

        /// <summary>
        /// Gets shell uploaded reports details
        /// </summary>
        /// <param name="assessmentId">Assessment ID</param>
        /// <returns>Gets uploaded file's schema information</returns>
        [HttpGet]
        [Route("shelltable/getUploadedQuestionBankShellTableFileInformation/{assessmentId}")]
        public async Task<ActionResult<bool>> GetUploadedQuestionBankShellTableFileInformationAsync(Guid assessmentId)
        {
            return Ok(await _assessmentQuestionQueries.GetUploadedQuestionBankShellTableFileInformationAsync(assessmentId));
        }

        /// <summary>
        /// Download health facility template
        /// </summary>        
        /// <returns>An excel file</returns>
        [HttpGet]
        [Route("healthFacilityTemplate/download")]
        public IActionResult DownloadHealthFacilityTemplate()
        {
            HttpContext.Response.ContentType = Constants.DownloadDocument.ContentTypeForExcel;

            string language = _translationService.GetCurrentCulture();
            string templateFilePath = Path.Combine(Environment.CurrentDirectory, $"{Constants.DownloadDocument.HealthFacilityFilePath}_{language}.xlsx");

            FileStream fileStream = new FileStream(templateFilePath, FileMode.Open);
            string fileName = $"{Constants.DownloadDocument.HealthFacilityTemplateFileName}_{language}.xlsx";
            return File(fileStream, "application/octet-stream", fileName);
        }

        // <summary>
        /// Get latest health facility data
        /// </summary>
        /// <param name="countryId">Country id for which health facilities data are to be fetched </param>
        /// <returns>List of latest health facility data for country</returns>
        [HttpGet]
        [Route("{countryId}/healthFacilities")]
        public async Task<ActionResult<HealthFacilityDto>> GetHealthFacilitiesAsync(Guid countryId)
        {
            return Ok(await _assessmentQuestionQueries.GetLatestHealthFacilitiesByCountryAsync(countryId));
        }

        /// <summary>
        /// Export latest health facilities data into excel
        /// </summary> 
        /// <param name="countryId">Country id for which health facilities data are to be fetched </param>
        /// <returns>An excel file</returns>
        [HttpGet]
        [Route("healthfacilities/export/{countryId}")]
        public async Task<FileContentResult> ExportHealthFacilityData(Guid countryId)
        {
            FileResponseDto fileResponseDto = await _assessmentQuestionQueries.GetHealthFacilitiesTemplateFileResponseAsync(base.GetCurrentUser().UserId, countryId);

            var result = new FileContentResult(fileResponseDto.FileData, Constants.DownloadDocument.ExcelFormat)
            {
                FileDownloadName = fileResponseDto.FileName
            };

            return result;
        }

        /// <summary>
        /// Export health facilities data into excel for assessment
        /// </summary>        
        /// <param name="assessmentId">Assessment id for which associated with the assessment</param>
        /// <param name="countryId">Country id for which health facilities data are to be fetched </param>
        /// <returns>An excel file</returns>
        [HttpGet]
        [Route("healthfacilities/export/{assessmentId}/{countryId}")]
        public async Task<FileContentResult> ExportHealthFacilityDataForAssessmentAsync(Guid assessmentId, Guid countryId)
        {
            Guid currentUserId = base.GetCurrentUser().UserId;

            FileResponseDto fileResponseDto = await _assessmentQuestionQueries.GetHealthFacilitiesTemplateFileResponseForAssessmentAsync(currentUserId, assessmentId, countryId);

            FileContentResult result = new FileContentResult(fileResponseDto.FileData, Constants.DownloadDocument.ExcelFormat)
            {
                FileDownloadName = fileResponseDto.FileName
            };

            return result;
        }
    }
}