﻿using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;

using MediatR;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Services.Handlers.Queries;

namespace WHO.MALARIA.Web.Apis
{
    /// <summary>
    /// API controllers which exposes APIs which are relevant for external users and doesn't require authentication
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ExternalController : BaseApiController
    {
        private readonly IAssessmentQueries _assessmentQueries;
        public ExternalController(IMediator mediator, IHttpContextAccessor httpContextAccessor, IAssessmentQueries assessmentQueries) : base(mediator, httpContextAccessor)
        {
            _assessmentQueries = assessmentQueries;
        }

        /// <summary>
        /// Returns the identity of a user based on the filter criteria passed else returns all
        /// </summary>
        /// <param name="filterCriterias">List of filter criteria</param>
        /// <returns></returns>
        [HttpPost]
        [ProducesResponseType(typeof(List<IdAndNameDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [Route("country/records")]
        public async Task<ActionResult> GetMultipleRecordsAsync()
        {
            GetEntityMultipleRecordsQuery query = await SerializeJObjectAsync(new GetEntityMultipleRecordsQuery());

            query.Entity = "Country";
            return Ok(await QueryAsync(new GetEntityMultipleRecordsQuery(query.Entity, query.FilterCriterias)));
        }

        /// <summary>
        /// Downloads all the malaria toolkit documents 
        /// </summary>
        /// <returns>Zip file containing all documents</returns>
        [HttpGet]
        [Route("tools/download")]
        public FileContentResult Download()
        {
            string language = HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;

            //path of the zip which is to be downloaded
            //string folderPath = string.Format(Directory.GetCurrentDirectory() + Constants.DownloadDocument.MalariaSurveillanceToolkitToolsFolderPath, language);
            string folderPath = Path.Combine(Directory.GetCurrentDirectory(), "Assets", language, "MalariaSurveillanceToolkitTools.zip");

            HttpContext.Response.ContentType = Constants.DownloadDocument.ContentType;

            FileContentResult result = new FileContentResult(System.IO.File.ReadAllBytes(folderPath), Constants.DownloadDocument.ContentType);

            return result;
        }

        /// <summary>
        /// Returns the different properties with assessment details to show on dashboard 
        /// </summary>
        /// <returns>DashboardAssessmentPropertiesDTO object.</returns>
        [HttpGet]
        [Route("dashboard/assessment/statistics")]
        public async Task<ActionResult<DashboardAssessmentStatisticsDTO>> GetDashboardStatistics()
        {
            return Ok(await _assessmentQueries.GetDashboardAssessmentStatisticsAsync());
        }

        /// <summary>
        /// Download overview pdf file
        /// </summary>
        /// <returns>An pdf file</returns>
        [HttpGet]
        [Route("download/overview-document")]
        public FileContentResult DownloadOverviewDocument()
        {
            string language = HttpContext?.Request?.Cookies[Constants.Common.I18next] ?? Constants.Common.DefaultLanguage;

            //string folderPath = string.Format(Directory.GetCurrentDirectory() + Constants.DownloadDocument.OverviewFilePath, language);
            string folderPath = Path.Combine(Directory.GetCurrentDirectory(), "Assets", language, "Introduction to the Malaria Surveillance Assessment Toolkit.pdf");

            byte[] bytes = System.IO.File.ReadAllBytes(folderPath);

            return new FileContentResult(bytes, Constants.DownloadDocument.PDFContentType);
        }
    }
}
