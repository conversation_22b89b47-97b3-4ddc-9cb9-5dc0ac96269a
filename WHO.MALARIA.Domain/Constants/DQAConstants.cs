﻿using System.Collections.Generic;
using System.IO;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.SeedingMetadata;

namespace WHO.MALARIA.Domain.Constants
{
    /// <summary>
    /// Contains constant values for Desk Level DQA
    /// </summary>
    public static class DQAConstants
    {
        //Caching Data systems
        public const string DeskLevelDataSystem1 = "DeskLevelDataSystem1";
        public const string DeskLevelDataSystem2 = "DeskLevelDataSystem2";
        public const string DiscrepancyForConsistancyBetweenVariables = "DiscrepancyForConsistancyBetweenVariables";
        public const string DiscrepancyForSameVariablesWithConcordance = "DiscrepancyForSameVariablesWithConcordance";
        public const string DQAVaribles = "DQAVaribles";

        //Report Names EN
        public const string NationalReport = "NationalLevelReport";
        public const string ProvinceReport = "ProvinceLevelReport";
        public const string DistrictReport = "DistrictLevelReport";
        public const string HealthFacilityReport = "HealthFacilityLevelReport";

        //DQA Indicator report headings EN
        public const string CompletenessOfReporting = "CompletenessOfReporting";
        public const string TimelinessOfReporting = "TimelinessOfReporting";
        public const string VariableCompleteness = "VariableCompleteness";
        public const string ConsistencyBetweenVariable = "ConsistencyBetweenVariable";
        public const string ConsistencyOverTime = "ConsistencyOverTime";
        public const string Concordance = "Concordance";

        //DQA Desk Level Graph Worksheets EN
        public const string CompletenessOfReportingGraph = "DataQualityAnalysis.1_2_1_charts";
        public const string TimelinessOfReportingGraph = "DataQualityAnalysis.1_2_3_charts";
        public const string VariableCompletenessGraph = "DataQualityAnalysis.1_2_7_charts";
        public const string ConsistencyBetweenVariableGraph = "DataQualityAnalysis.1_2_8_charts";
        public const string ConsistencyOverTimeGraph = "DataQualityAnalysis.1_2_9_charts";
        public const string ConcordanceGraph = "DataQualityAnalysis.1_2_10_charts";

        //CompletenessOfReporting summary report headers
        public const string CompletenessReportNumeratorHeader = "CompletenessReportNumeratorHeader";
        public const string CompletenessReportDenomeratorHeader = "CompletenessReportDenomeratorHeader";
        public const string CompletenessReportResultHeader = "CompletenessReportResultHeader";

        //ConsistencyBetweenVariablesOfReporting summary report headers EN
        public const string ConsistencyReportNumeratorHeader = "ConsistencyReportNumeratorHeader";
        public const string ConsistencyReportDenomeratorHeader = "ConsistencyReportDenomeratorHeader";
        public const string ConsistencyReportResultHeader = "ConsistencyReportResultHeader";

        //ConcordanceReporting summary report headers EN
        public const string ConcordanceReportNumeratorHeader = "ConcordanceReportNumeratorHeader";
        public const string ConcordanceReportDenomeratorHeader = "ConcordanceReportDenomeratorHeader";
        public const string ConcordanceReportResultHeader = "ConcordanceReportResultHeader";

        
        //National Level Summary Tab EN
        public const string SummaryReportName = "SummaryReportName";
        public const string SummaryReportTitle = "SummaryReportName";


        //Elimination DQA 
        public static readonly string DQATemplateFilePath = Path.Combine("Templates", "DQA");

        //Elimination DQA National Summary Parameters
        public const string DQAReportCompleteness = "Assessment.DataCollection.DataQualityAssessment.ReportCompleteness";
        public const string DQACaseInvestigationReportsCompleteness = "Assessment.DataCollection.DataQualityAssessment.CaseInvestigationReportsCompleteness";
        public const string DQAReportTimeliness = "Assessment.DataCollection.DataQualityAssessment.ReportTimeliness";
        public const string DQACaseNotificationReportsTimeliness = "Assessment.DataCollection.DataQualityAssessment.CaseNotificationReportsTimeliness";
        public const string DQACaseInvestigationReportsTimeliness = "Assessment.DataCollection.DataQualityAssessment.CaseInvestigationReportsTimeliness";
        public const string DQAFociInvestigationReportsTimeliness = "Assessment.DataCollection.DataQualityAssessment.FociInvestigationReportsTimeliness";
        public const string DQACoreVariableCompletenessWithinReport = "Assessment.DataCollection.DataQualityAssessment.CoreVariableCompletenessWithinReport";
        public const string DQAConsistencyBetweenCoreVariables = "Assessment.DataCollection.DataQualityAssessment.ConsistencyBetweenCoreVariables";
        public const string DQAConsistencyOverTimeCoreIndicators = "Assessment.DataCollection.DataQualityAssessment.ConsistencyOvertimeCoreIndicators";
        public const string DQAConfirmMalariaCasesNotified = "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesNotified";
        public const string DQAConfirmMalariaCasesInvestigated = "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesInvestigated";
        public const string DQAConfirmMalariaCasesClassified = "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesClassified";
        public const string DQAConfirmMalariaCasesClassifiedAsLocal = "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesClassifiedAsLocal";
        public const string DQAConfirmMalariaCasesClassifiedAsIndigenous = "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesClassifiedAsIndigenous";
        public const string DQAConfirmMalariaCasesClassifiedAsIntroduced = "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesClassifiedAsIntroduced";
        public const string DQAConfirmMalariaCasesClassifiedAsImported = "Assessment.DataCollection.DataQualityAssessment.ConfirmMalariaCasesClassifiedAsImported";
        public const string DQAMalariaCasesDueToPF = "Assessment.DataCollection.DataQualityAssessment.MalariaCasesDueToPF";
        public const string DQAMalariaCasesDueToPK = "Assessment.DataCollection.DataQualityAssessment.MalariaCasesDueToPK";
        public const string DQAMalariaCasesDueToPM = "Assessment.DataCollection.DataQualityAssessment.MalariaCasesDueToPM";
        public const string DQAMalariaCasesDueToPO = "Assessment.DataCollection.DataQualityAssessment.MalariaCasesDueToPO";
        public const string DQAMalariaCasesDueToPV = "Assessment.DataCollection.DataQualityAssessment.MalariaCasesDueToPV";
        public const string DQAKeyVariableConcordanceBtwTwoReportingSystem = "Assessment.DataCollection.DataQualityAssessment.KeyVariableConcordanceBtwTwoReportingSystem";
        public const string DQACoreVariableCompletenessWithinRegister = "Assessment.DataCollection.DataQualityAssessment.CoreVariableCompletenessWithinRegister";
        public const string DQACoreVariableConcordanceBtwRegister = "Assessment.DataCollection.DataQualityAssessment.CoreVariableConcordanceBtwRegister";

        public const string DQASummaryNationalDataQualityEstimates = "Assessment.DataCollection.DataQualityAssessment.SummaryNationalDataQualityEstimates";
        public const string DQASummaryPercentage = "Assessment.DataCollection.DataQualityAssessment.SummaryPercentage";

        public const string DataQualityResultReason = "Assessment.DataCollection.DataQualityAssessment.Desc1_2_14";

        //Consistency Over time indicator constants in EN
        public const string ProportionOfMalariaOutpatients = "ProportionOfMalariaOutpatients";
        public const string ProportionOfMalariaInpatients = "ProportionOfMalariaInpatients";
        public const string ProportionOfMalariaInpatientDeaths = "ProportionOfMalariaInpatientDeaths";
        public const string TestPositivityRate = "TestPositivityRate";
        public const string SlidePositivityRate = "SlidePositivityRate";
        public const string RDTPositivityRate = "RDTPositivityRate";
        public const string ProportionOfSuspectsTested = "ProportionOfSuspectsTested";

        public const string NationalLevel = "DataQualityAnalysis.NationalLevel";
        public const string ProvinceLevel = "DataQualityAnalysis.ProvinceLevel";
        public const string DistrictLevel = "DataQualityAnalysis.DistrictLevel";
        public const string HealthFacilityLevel = "DataQualityAnalysis.HealthFacilityLevel";


        public const string HealthFacilitySummary = "DataQualityAnalysis.HealthFacilitySummary";
        public const string NationalLevelSummary = "DataQualityAnalysis.NationalLevelSummary";
        public const string ProvinceLevelSummary = "DataQualityAnalysis.ProvinceLevelSummary";

        public const string ProvinceSummary = "DataQualityAnalysis.ProvinceSummary";
        public const string DistrictSummary = "DataQualityAnalysis.DistrictSummary";

        public const string National = "Common.National";
        public const string Province = "DataQualityAnalysis.Province";
        public const string District = "Common.District";
        public const string HealthFacility = "DataQualityAnalysis.HealthFacilityName";

        public const string PriorityVariable = "DataQualityAnalysis.PriorityVariable";
        public const string OptionalVariable = "DataQualityAnalysis.OptionalVariable";

        public const string PriorityVariablePercentage = "DataQualityAnalysis.PriorityVariablePercentage";
        public const string OptionalVariablePercentage = "DataQualityAnalysis.OptionalVariablePercentage";

        public const string NationalLevelPercentage = "DataQualityAnalysis.NationalLevelPercentage";
        public const string ProvinceLevelPercentage = "DataQualityAnalysis.ProvinceLevelPercentage";
        public const string DistrictLevelPercentage = "DataQualityAnalysis.DistrictLevelPercentage";

        public const int MinmunNumberOfYearsToShowInGraphXAxis = 5;

        public static List<DQAVariableDto> BasicColumns = new List<DQAVariableDto>
            {
                new DQAVariableDto { Id = DQAVariableSeedingMetadata.Province_ID,Name="Province",Name_FR="Province"},
                new DQAVariableDto { Id = DQAVariableSeedingMetadata.District_ID,Name="District",Name_FR="District"},
                new DQAVariableDto { Id = DQAVariableSeedingMetadata.HealthFacility_ID,Name="Health Facility",Name_FR="Établissement de santé"},
                new DQAVariableDto { Id = DQAVariableSeedingMetadata.PublicPrivate_ID,Name="Public / Private",Name_FR="Public / Privé"},
                new DQAVariableDto { Id = DQAVariableSeedingMetadata.Year_ID,Name="Year",Name_FR="Année"},
                new DQAVariableDto { Id = DQAVariableSeedingMetadata.Month_ID,Name="Month",Name_FR="Mois"},
                new DQAVariableDto { Id = DQAVariableSeedingMetadata.ReportsOnTime_ID,Name="Reports On Time",Name_FR="Rapports ponctuels"},
                new DQAVariableDto { Id = DQAVariableSeedingMetadata.ReportsReceived_ID,Name="Reports Received",Name_FR="Rapports reçus"},
                new DQAVariableDto { Id = DQAVariableSeedingMetadata.ExpectedReports_ID,Name="Expected Reports",Name_FR="Rapports attendus"}
            };


    }
}
