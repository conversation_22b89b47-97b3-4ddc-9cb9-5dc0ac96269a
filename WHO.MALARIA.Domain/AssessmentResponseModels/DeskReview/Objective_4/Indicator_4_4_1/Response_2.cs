﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_4.Indicator_4_4_1
{
    /// <summary>
    /// Contains desk review MDA response properties for Indicator 4.4.1
    /// </summary>
    public class Response_2 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public bool HasTraining { get; set; }

        public SurveillanceTask DataCollection { get; set; }

        public SurveillanceTask DataReporting { get; set; }

        public SurveillanceTask DataQualityReview { get; set; }

        public SurveillanceTask DataAnalysis { get; set; }

        public SurveillanceTask DisseminationReport { get; set; }

        public SurveillanceTask Supervision { get; set; }

        public SurveillanceTask AdverseEvent { get; set; }

        public SurveillanceTask DrugResistance { get; set; }

        public SurveillanceTask PublicPrivateSectorTraining { get; set; }

        public TaskTrainingDetail Attendant { get; set; }

        public TaskTrainingDetail TrainingFrequency { get; set; }

        public TrainingDateDetails LastDateOfTraining { get; set; }

        /// <summary>
        /// Validates indicator 4.4.1
        /// </summary>
        /// <returns>Validation results for indicator 4.4.1</returns>
        public ValidationResult Validate()
        {
            return new Response_2_Validator().Validate(this);
        }

        /// <summary>
        ///Get analytical output indicator response of table type for indicator 4.4.1
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>
        /// <returns>Analytical output indicator response of table type for indicator 4.4.1</returns>
        public List<TaskTrainingDetail> GetAnalyticalTableResponse(Delegate translator)
        {
            List<TaskTrainingDetail> taskTrainingDetail = new List<TaskTrainingDetail>();

            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.DataCollection_4_4_1).ToString(), translator.DynamicInvoke(DataCollection.NationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DataCollection.SubNationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DataCollection.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.DataReporting_4_4_1).ToString(), translator.DynamicInvoke(DataReporting.NationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DataReporting.SubNationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DataReporting.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.ConductingDataQualityReview_4_4_1).ToString(), translator.DynamicInvoke(DataQualityReview.NationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DataQualityReview.SubNationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DataQualityReview.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.ConductingDataAnalysis_4_4_1).ToString(), translator.DynamicInvoke(DataAnalysis.NationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DataAnalysis.SubNationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DisseminationReport.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.PreparingDisseminationReports_4_4_1).ToString(), translator.DynamicInvoke(DisseminationReport.NationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DisseminationReport.SubNationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Supervision.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.Supervision_4_4_1).ToString(), translator.DynamicInvoke(Supervision.NationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Supervision.SubNationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Supervision.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.MDAMonitoringAdverseEvents_4_4_1).ToString(), translator.DynamicInvoke(AdverseEvent.NationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(AdverseEvent.SubNationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(AdverseEvent.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.MDAMonitoringDrugResistance_4_4_1).ToString(), translator.DynamicInvoke(DrugResistance.NationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DrugResistance.SubNationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DrugResistance.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.TrainingInPublicPrivateSectors_4_4_1).ToString(), translator.DynamicInvoke(PublicPrivateSectorTraining.NationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(PublicPrivateSectorTraining.SubNationalLevel.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(PublicPrivateSectorTraining.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.Attendants_4_4_1).ToString(), Attendant.NationalLevel, Attendant.SubNationalLevel, Attendant.ServiceDeliveryLevel));
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.FrequencyOfTraining_4_4_1).ToString(), TrainingFrequency.NationalLevel, TrainingFrequency.SubNationalLevel, TrainingFrequency.ServiceDeliveryLevel));
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.DateOfLastTraining_4_4_1).ToString(), AnalyticalOutputHelper.ConvertDateTimeToString(LastDateOfTraining?.National), AnalyticalOutputHelper.ConvertDateTimeToString(LastDateOfTraining?.SubNational), AnalyticalOutputHelper.ConvertDateTimeToString(LastDateOfTraining?.ServiceDeliveryLevel)));

            return taskTrainingDetail;
        }

        /// <summary>
        /// Get analytical report response for indicator 4.4.1
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param> 
        /// <returns>Object of analytical output indicator response dto</returns> 
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator)
        {
            List<TaskTrainingDetail> taskTrainingDetail = GetAnalyticalTableResponse(translator);
            
            taskTrainingDetail.Add(new TaskTrainingDetail(translator.DynamicInvoke(AnalyticalOutputConstants.ProportionOfRelevantStaff_4_4_1).ToString(), GetNationalPercentage(), GetSubNationalPercentage(), GetServiceDeliveryPercentage()));

            AnalyticalOutputType outputType = AnalyticalOutputType.Table;

            TableResponse table = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(TaskTrainingDetail), taskTrainingDetail, translator);

            table.HasCalculation = true;

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                Type = (int)outputType,
                Response = table
            };

            return response;
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="indicatorSequence">Contains name of indicator</param> 
        /// <returns>Indicator 4.4.1 response in the form of data table</returns>
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence)
        {
            DataSet ds = new DataSet();

            List<TaskTrainingDetail> taskTrainingDetail = GetAnalyticalTableResponse(translator);

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(TaskTrainingDetail), taskTrainingDetail, indicatorSequence, translator);

            dt.Rows.Add();

            dt.Rows.Add(translator.DynamicInvoke(AnalyticalOutputConstants.ProportionOfRelevantStaff_4_4_1).ToString(), GetNationalPercentage(), GetSubNationalPercentage(), GetServiceDeliveryPercentage());

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        /// Get national percentage
        /// </summary>
        /// <returns>National percentage</returns>
        private string GetNationalPercentage()
        {
            List<bool?> nationalData = new List<bool?>() { DataCollection.NationalLevel, DataReporting.NationalLevel, DataQualityReview.NationalLevel, DataAnalysis.NationalLevel, DisseminationReport.NationalLevel, Supervision.NationalLevel, AdverseEvent.NationalLevel, DrugResistance.NationalLevel, PublicPrivateSectorTraining.NationalLevel };

            int nationalPercentage = AnalyticalOutputHelper.CalculatePercentage(nationalData.Count, nationalData.Count(value => value == true));

            return $"{nationalPercentage}%";
        }

        /// <summary>
        /// Get subNational percentage
        /// </summary>
        /// <returns>Subnational percentage</returns>
        private string GetSubNationalPercentage()
        {
            List<bool?> subNationalData = new List<bool?>() { DataCollection.SubNationalLevel, DataReporting.SubNationalLevel, DataQualityReview.SubNationalLevel, DataAnalysis.SubNationalLevel, DisseminationReport.SubNationalLevel, Supervision.SubNationalLevel, AdverseEvent.SubNationalLevel, DrugResistance.SubNationalLevel, PublicPrivateSectorTraining.SubNationalLevel };

            int subNationalPercentage = AnalyticalOutputHelper.CalculatePercentage(subNationalData.Count, subNationalData.Count(value => value == true));

            return $"{subNationalPercentage}%";
        }

        /// <summary>
        /// Calculate service delivery percentage
        /// </summary>
        /// <returns>Service delivery percentage</returns>
        private string GetServiceDeliveryPercentage()
        {
            List<bool?> serviceDeliveryData = new List<bool?>() { DataCollection.ServiceDeliveryLevel, DataReporting.ServiceDeliveryLevel, DataQualityReview.ServiceDeliveryLevel, DataAnalysis.ServiceDeliveryLevel, DisseminationReport.ServiceDeliveryLevel, Supervision.ServiceDeliveryLevel, AdverseEvent.ServiceDeliveryLevel, DrugResistance.ServiceDeliveryLevel, PublicPrivateSectorTraining.ServiceDeliveryLevel };

            int serviceDeliveryPercentage = AnalyticalOutputHelper.CalculatePercentage(serviceDeliveryData.Count, serviceDeliveryData.Count(value => value == true));

            return $"{serviceDeliveryPercentage}%";
        }
    }

    /// <summary>
    /// Contains training details to describe surveillance tasks which are covered for MDA
    /// </summary>
    public class SurveillanceTask
    {
        public bool? NationalLevel { get; set; }

        public bool? SubNationalLevel { get; set; }

        public bool? ServiceDeliveryLevel { get; set; }
    }
    /// <summary>
    /// Contains training details to describe surveillance tasks which are covered for MDA
    /// </summary>
    public class TaskTrainingDetail
    {
        [TableColumn(Name = "Training", TranslationKey = "DRObjective_4_Responses.Indicator_4_4_1.Training", Width = Common.Width300, Order = 1)]
        public string Training { get; set; }
        [TableColumn(Name = "NationalLevel", TranslationKey = "DRObjective_4_Responses.Indicator_4_4_1.NationalLevel", Width = Common.Width200, Order = 2)]
        public string NationalLevel { get; set; }
        [TableColumn(Name = "SubNationalLevel", TranslationKey = "DRObjective_4_Responses.Indicator_4_4_1.SubnationalLevel", Width = Common.Width200, Order = 3)]
        public string SubNationalLevel { get; set; }
        [TableColumn(Name = "ServiceDeliveryLevel", TranslationKey = "DRObjective_4_Responses.Indicator_4_4_1.ServiceDeliveryLevel", Width = Common.Width200, Order = 4)]
        public string ServiceDeliveryLevel { get; set; }

        public TaskTrainingDetail(string training, string nationalLevel, string subNationalLevel, string serviceDeliveryLevel)
        {
            Training = training;
            NationalLevel = nationalLevel;
            SubNationalLevel = subNationalLevel;
            ServiceDeliveryLevel = serviceDeliveryLevel;
        }
    }    
}
