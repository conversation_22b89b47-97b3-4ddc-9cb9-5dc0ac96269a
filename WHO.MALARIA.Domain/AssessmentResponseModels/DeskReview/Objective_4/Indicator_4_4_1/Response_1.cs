﻿using FluentValidation.Results;
using System;
using System.Collections.Generic;
using System.Data;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos.OutputDtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Helper;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_4.Indicator_4_4_1
{
    /// <summary>
    /// Contains desk review response properties for Indicator 4.4.1
    /// </summary>
    public class Response_1 : AssessmentResponseBase, IResponseValidator
    {
        public bool CannotBeAssessed { get; set; }

        public string CannotBeAssessedReason { get; set; }

        public string MetNotMetStatus { get; set; }

        public bool HasTraining { get; set; }

        public MalariaSurveillanceTraining DataCollection { get; set; }

        public MalariaSurveillanceTraining DataReporting { get; set; }

        public MalariaSurveillanceTraining ConductingDataQualityReview { get; set; }

        public MalariaSurveillanceTraining ConductingDataAnalysis { get; set; }

        public MalariaSurveillanceTraining PreparingDisseminationReports { get; set; }

        public MalariaSurveillanceTraining Supervision { get; set; }

        public MalariaSurveillanceTraining CaseNotification { get; set; }

        public MalariaSurveillanceTraining CaseInvestigation { get; set; }

        public MalariaSurveillanceTraining CaseClassification { get; set; }

        public MalariaSurveillanceTraining FociInvestigation { get; set; }

        public MalariaSurveillanceTraining FociClassification { get; set; }

        public MalariaSurveillanceTraining QualityAssuranceOfLabData { get; set; }

        public MalariaSurveillanceTraining TrainingForMicroscopy { get; set; }

        public MalariaSurveillanceTraining TrainingInPublicPrivateSectors { get; set; }

        public TrainingDetails Attendants { get; set; }

        public TrainingDetails FrequencyOfTraining { get; set; }

        public TrainingDateDetails LastDateOfTraining { get; set; }

        public TrainingDetail PlannedTraining { get; set; }

        public TrainingDetail PreviousYearTraining { get; set; }

        //National training proportion percentage is used to calculate the percentage of proportion for number of trainings/ number of planned trainings in the previous year and based on this field FE validation are added in validation rules
        //Not saved in DB 
        public int? NationalTrainingProportion { get; set; }

        //Subnational training proportion percentage is used to calculate the percentage of proportion for number of trainings/ number of planned trainings in the previous year and based on this field FE validation are added in validation rules
        //Not saved in DB
        public int? SubNationalTrainingProportion { get; set; }

        //Service delivery training proportion percentage is used to calculate the percentage of proportion for number of trainings/ number of planned trainings in the previous year and based on this field FE validation are added in validation rules
        //Not saved in DB
        public int? ServiceDeliveryTrainingProportion { get; set; }

        /// <summary>
        /// Validates indicator 4.4.1
        /// </summary>
        /// <returns>Validation results for indicator 4.4.1</returns>
        public ValidationResult Validate()
        {
            return new Response_1_Validator().Validate(this);
        }

        /// <summary>
        ///Get analytical output indicator response of table type for indicator 4.4.1
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>
        /// <returns>Analytical output indicator response of table type for indicator 4.4.1</returns>
        public List<TrainingDetails> GetAnalyticalTableResponse(Delegate translator)
        {
            List<TrainingDetails> trainingDetails = new List<TrainingDetails>();

            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.DataCollection_4_4_1).ToString(), translator.DynamicInvoke(DataCollection.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DataCollection.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DataCollection.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.DataReporting_4_4_1).ToString(), translator.DynamicInvoke(DataReporting.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DataReporting.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(DataReporting.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.ConductingDataQualityReview_4_4_1).ToString(), translator.DynamicInvoke(ConductingDataQualityReview.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(ConductingDataQualityReview.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(ConductingDataQualityReview.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.ConductingDataAnalysis_4_4_1).ToString(), translator.DynamicInvoke(ConductingDataAnalysis.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(ConductingDataAnalysis.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(ConductingDataAnalysis.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.PreparingDisseminationReports_4_4_1).ToString(), translator.DynamicInvoke(PreparingDisseminationReports.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(PreparingDisseminationReports.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Supervision.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.Supervision_4_4_1).ToString(), translator.DynamicInvoke(Supervision.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Supervision.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(Supervision.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.CaseNotification_4_4_1).ToString(), translator.DynamicInvoke(CaseNotification.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(CaseNotification.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(CaseNotification.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.CaseInvestigation_4_4_1).ToString(), translator.DynamicInvoke(CaseInvestigation.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(CaseInvestigation.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(CaseInvestigation.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.CaseClassification_4_4_1).ToString(), translator.DynamicInvoke(CaseClassification.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(CaseClassification.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(CaseClassification.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.FociInvestigation_4_4_1).ToString(), translator.DynamicInvoke(FociInvestigation.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(FociInvestigation.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(FociInvestigation.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.FociClassification_4_4_1).ToString(), translator.DynamicInvoke(FociClassification.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(FociClassification.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(FociClassification.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.QualityAssuranceOfLabData_4_4_1).ToString(), translator.DynamicInvoke(QualityAssuranceOfLabData.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(QualityAssuranceOfLabData.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(QualityAssuranceOfLabData.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.TrainingForMicroscopy_4_4_1).ToString(), translator.DynamicInvoke(TrainingForMicroscopy.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(TrainingForMicroscopy.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(TrainingForMicroscopy.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.TrainingInPublicPrivateSectors_4_4_1).ToString(), translator.DynamicInvoke(TrainingInPublicPrivateSectors.National.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(TrainingInPublicPrivateSectors.SubNational.ConvertBoolToYesNo()).ToString(), translator.DynamicInvoke(TrainingInPublicPrivateSectors.ServiceDeliveryLevel.ConvertBoolToYesNo()).ToString()));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.Attendants_4_4_1).ToString(), Attendants.National, Attendants.SubNational, Attendants.ServiceDeliveryLevel));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.FrequencyOfTraining_4_4_1).ToString(), FrequencyOfTraining.National, FrequencyOfTraining.SubNational, FrequencyOfTraining.ServiceDeliveryLevel));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.DateOfLastTraining_4_4_1).ToString(), AnalyticalOutputHelper.ConvertDateTimeToString(LastDateOfTraining?.National), AnalyticalOutputHelper.ConvertDateTimeToString(LastDateOfTraining?.SubNational), AnalyticalOutputHelper.ConvertDateTimeToString(LastDateOfTraining?.ServiceDeliveryLevel)));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.PlannedTrainings_4_4_1).ToString(), PlannedTraining.National.ToString(), PlannedTraining.SubNational.ToString(), FrequencyOfTraining.ServiceDeliveryLevel));
            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.PreviousYearTrainings_4_4_1).ToString(), PreviousYearTraining.National.ToString(), PreviousYearTraining.SubNational.ToString(), PreviousYearTraining.ServiceDeliveryLevel.ToString()));

            return trainingDetails;
        }

        /// <summary>
        /// Get analytical report response for indicator 4.4.1
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param> 
        /// <returns>Object of analytical output indicator response dto</returns> 
        public AnalyticalOutputIndicatorResponseDto BuildReportResponse(Delegate translator)
        {
            List<TrainingDetails> trainingDetails = GetAnalyticalTableResponse(translator);

            trainingDetails.Add(new TrainingDetails(translator.DynamicInvoke(AnalyticalOutputConstants.ProportionOfPlannedTrainings_4_4_1).ToString(), GetNationalPercentage(), GetSubNationalPercentage(), GetServiceDeliveryPercentage()));

            AnalyticalOutputType outputType = AnalyticalOutputType.Table;

            TableResponse table = AnalyticalOutputIndicatorResponseHelper.GetAnalyticalOutputIndicatorTable(typeof(TrainingDetails), trainingDetails, translator);

            table.HasCalculation = true;

            AnalyticalOutputIndicatorResponseDto response = new AnalyticalOutputIndicatorResponseDto
            {
                Type = (int)outputType,
                Response = table
            };

            return response;
        }

        /// <summary>
        /// Process indicator response and produce the result that can be exported
        /// </summary>
        /// <param name="translator">Delegate object which is used for translation</param>        
        /// <param name="indicatorSequence">Contains name of indicator</param> 
        /// <returns>Indicator 4.4.1 response in the form of data table</returns>
        public TabularDataInputModel BuildAnalyticalReport(Delegate translator, string indicatorSequence)
        {
            DataSet ds = new DataSet();

            List<TrainingDetails> trainingDetails = GetAnalyticalTableResponse(translator);

            DataTable dt = AnalyticalOutputHelper.GetDataTable(typeof(TrainingDetails), trainingDetails, indicatorSequence, translator);

            dt.Rows.Add();

            dt.Rows.Add(translator.DynamicInvoke(AnalyticalOutputConstants.ProportionOfPlannedTrainings_4_4_1).ToString(), GetNationalPercentage(), GetSubNationalPercentage(), GetServiceDeliveryPercentage());

            ds.Tables.Add(dt);

            TabularDataInputModel tabularData = new TabularDataInputModel
            {
                SheetName = indicatorSequence,
                Tables = ds
            };

            return tabularData;
        }

        /// <summary>
        /// Get national percentage
        /// </summary>
        /// <returns>National percentage</returns>
        private string GetNationalPercentage()
        {
            int nationalPercentage = AnalyticalOutputHelper.CalculatePercentage(Convert.ToInt32(PlannedTraining.National), Convert.ToInt32(PreviousYearTraining.National));

            return $"{nationalPercentage}%";
        }

        /// <summary>
        /// Get subNational percentage
        /// </summary>
        /// <returns>Subnational percentage</returns>
        private string GetSubNationalPercentage()
        {
            int subNationalPercentage = AnalyticalOutputHelper.CalculatePercentage(Convert.ToInt32(PlannedTraining.SubNational), Convert.ToInt32(PreviousYearTraining.SubNational));

            return $"{subNationalPercentage}%";
        }

        /// <summary>
        /// Get service delivery percentage
        /// </summary>
        /// <returns>Service delivery percentage</returns>
        private string GetServiceDeliveryPercentage()
        {
            int serviceDeliveryPercentage = AnalyticalOutputHelper.CalculatePercentage(Convert.ToInt32(PlannedTraining.ServiceDeliveryLevel), Convert.ToInt32(PreviousYearTraining.ServiceDeliveryLevel));

            return $"{serviceDeliveryPercentage}%";
        }
    }

    /// <summary>
    /// Contains details of MalariaSurveillanceTraining
    /// </summary>
    public class MalariaSurveillanceTraining
    {
        public bool? National { get; set; }

        public bool? SubNational { get; set; }

        public bool? ServiceDeliveryLevel { get; set; }
    }
    /// <summary>
    /// Contains details of Training Details
    /// </summary>
    public class TrainingDetails
    {
        [TableColumn(Name = "Training", TranslationKey = "DRObjective_4_Responses.Indicator_4_4_1.Training", Width = Common.Width300, Order = 1)]
        public string Training { get; set; }

        [TableColumn(Name = "National", TranslationKey = "DRObjective_4_Responses.Indicator_4_4_1.NationalLevel", Width = Common.Width200, Order = 2)]
        public string National { get; set; }

        [TableColumn(Name = "SubNational", TranslationKey = "DRObjective_4_Responses.Indicator_4_4_1.SubnationalLevel", Width = Common.Width200, Order = 3)]
        public string SubNational { get; set; }

        [TableColumn(Name = "ServiceDeliveryLevel", TranslationKey = "DRObjective_4_Responses.Indicator_4_4_1.ServiceDeliveryLevel", Width = Common.Width200, Order = 4)]
        public string ServiceDeliveryLevel { get; set; }

        public TrainingDetails(string training, string national, string subNational, string serviceDeliveryLevel)
        {
            Training = training;
            National = national;
            SubNational = subNational;
            ServiceDeliveryLevel = serviceDeliveryLevel;
        }
    }

    /// <summary>
    /// Contains details of Training Date Details
    /// </summary>
    public class TrainingDateDetails
    {
        [TableColumn(Name = "National", TranslationKey = "DRObjective_4_Responses.Indicator_4_4_1.NationalLevel", Width = Common.Width200, Order = 2)]
        public DateTime? National { get; set; }

        [TableColumn(Name = "SubNational", TranslationKey = "DRObjective_4_Responses.Indicator_4_4_1.SubnationalLevel", Width = Common.Width200, Order = 3)]
        public DateTime? SubNational { get; set; }

        [TableColumn(Name = "ServiceDeliveryLevel", TranslationKey = "DRObjective_4_Responses.Indicator_4_4_1.ServiceDeliveryLevel", Width = Common.Width200, Order = 4)]
        public DateTime? ServiceDeliveryLevel { get; set; }
        public TrainingDateDetails(DateTime? national, DateTime? subNational, DateTime? serviceDeliveryLevel)
        {
            National = national;
            SubNational = subNational;
            ServiceDeliveryLevel = serviceDeliveryLevel;
        }
    }
    /// <summary>
    /// Contains training details
    /// </summary>
    public class TrainingDetail
    {
        public int? National { get; set; }

        public int? SubNational { get; set; }

        public int? ServiceDeliveryLevel { get; set; }
    }
}
