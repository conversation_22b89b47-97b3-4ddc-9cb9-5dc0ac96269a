﻿using ClosedXML.Excel;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using WHO.MALARIA.DocumentManager.Exceptions;
using WHO.MALARIA.DocumentManager.Models;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.DocumentManager
{
    /// <summary>
    /// Contains methods for question bank document(s) to create/process documents 
    /// </summary>
    public class QuestionBank : IQuestionBank
    {
        private readonly QuestionBankExcelSetting _questionBankExcelSettings;

        public QuestionBank(IOptions<QuestionBankExcelSetting> questionBankExcelSettings)
        {
            _questionBankExcelSettings = questionBankExcelSettings.Value;
        }

        #region  Public Method

        /// <summary>
        /// Modify existing question bank excel file template
        /// </summary>
        /// <param name="createQuestionDocumetInputModel">Object of createQuestionDocumetInputModel</param>
        /// <param name="language"> language code</param>
        /// <returns>byte[]</returns>
        public byte[] ProcessExcel(QuestionDocumentInputModel questionDocumentInputModel, string language)
        {
            bool isDefaultlanguage = language == WHO.MALARIA.Common.Constants.Common.DefaultLanguage;

            using (MemoryStream memoryStream = new MemoryStream())
            {
                string templateFilePath = Path.Combine(Environment.CurrentDirectory, $"{Constants.QuestionBankTemplateFilePath}_{language}.xlsx");

                using (XLWorkbook workbook = new XLWorkbook(templateFilePath))
                {
                    if (!questionDocumentInputModel.HasSelfAssessmentQuestions)
                    {
                        RemoveSelfAssessedQuestionsSheets(workbook, isDefaultlanguage);
                    }

                    RemoveSheetIfNotInRespondentType(workbook, questionDocumentInputModel.RespondentTypes, isDefaultlanguage);

                    List<Question> withoutSelfAssesmentQuestion = questionDocumentInputModel.Questions.Where(x => x.ForSelfAssessment == false).ToList();

                    // check for Question 3.2.1 1 concat master options with dynamic entered value
                    // currently only one guid is present in array
                    foreach (Guid questionsId in _questionBankExcelSettings.EditableQuestionsIds)
                    {
                        List<Question> questions = withoutSelfAssesmentQuestion.Where(x => x.QuestionId == questionsId).ToList();
                        //max one or two question here
                        questions.ForEach((question) =>
                        {
                            question.ResponseOption = $"{question.ResponseOption}\n{question.StaticResponseOption}";
                        });
                    }

                    IEnumerable<Question> withSelfAssesmentQuestion = questionDocumentInputModel.Questions.Where(x => x.ForSelfAssessment == true);

                    #region Subnational sheet
                    if (questionDocumentInputModel.RespondentTypes.Any(id => id == Convert.ToInt32(QBRespondentType.SubnationalLevel)))
                    {
                        IXLWorksheet subNationalSheet = workbook.Worksheet(
                            isDefaultlanguage ? _questionBankExcelSettings.SubNationalSheet.Name : _questionBankExcelSettings.SubNationalSheet.Name_FR);

                        CreateTemplateSheet(subNationalSheet, _questionBankExcelSettings.AssessmentSheet.AssessmentSheetStartingRowNumber, Convert.ToInt32(QBRespondentType.SubnationalLevel), withoutSelfAssesmentQuestion);

                        if (questionDocumentInputModel.HasSelfAssessmentQuestions)
                        {
                            IXLWorksheet subNationalSelfAssessmentSheet = workbook.Worksheet(
                                isDefaultlanguage ? _questionBankExcelSettings.SubNationalSelfAssessmentSheet.Name : _questionBankExcelSettings.SubNationalSelfAssessmentSheet.Name_FR);

                            CreateTemplateSheet(subNationalSelfAssessmentSheet, _questionBankExcelSettings.AssessmentSheet.SelfAssessmentSheetStartingRowNumber, Convert.ToInt32(QBRespondentType.SubnationalLevel), withSelfAssesmentQuestion);
                        }
                    }
                    #endregion

                    #region Service delivery level sheet

                    if (questionDocumentInputModel.RespondentTypes.Any(id => id == Convert.ToInt32(QBRespondentType.ServiceDeliveryLevel)))
                    {
                        IXLWorksheet serviceLevelSheet = workbook.Worksheet(isDefaultlanguage ?
                            _questionBankExcelSettings.ServiceLevelSheet.Name : _questionBankExcelSettings.ServiceLevelSheet.Name_FR);

                        CreateTemplateSheet(serviceLevelSheet, _questionBankExcelSettings.AssessmentSheet.AssessmentSheetStartingRowNumber, Convert.ToInt32(QBRespondentType.ServiceDeliveryLevel), withoutSelfAssesmentQuestion);

                        if (questionDocumentInputModel.HasSelfAssessmentQuestions)
                        {
                            IXLWorksheet serviceLevelSelfAssessmentSheet = workbook.Worksheet(
                                isDefaultlanguage ? _questionBankExcelSettings.ServiceLevelSelfAssessmentSheet.Name : _questionBankExcelSettings.ServiceLevelSelfAssessmentSheet.Name_FR);

                            CreateTemplateSheet(serviceLevelSelfAssessmentSheet, _questionBankExcelSettings.AssessmentSheet.SelfAssessmentSheetStartingRowNumber, Convert.ToInt32(QBRespondentType.ServiceDeliveryLevel), withSelfAssesmentQuestion);
                        }
                    }
                    #endregion

                    #region Community level sheet

                    if (questionDocumentInputModel.RespondentTypes.Any(id => id == Convert.ToInt32(QBRespondentType.CommunityLevel)))
                    {
                        IXLWorksheet communityLevelSheet = workbook.Worksheet(isDefaultlanguage ? _questionBankExcelSettings.CommunityLevelSheet.Name : _questionBankExcelSettings.CommunityLevelSheet.Name_FR);

                        CreateTemplateSheet(communityLevelSheet, _questionBankExcelSettings.AssessmentSheet.AssessmentSheetStartingRowNumber, Convert.ToInt32(QBRespondentType.CommunityLevel), withoutSelfAssesmentQuestion);

                        if (questionDocumentInputModel.HasSelfAssessmentQuestions)
                        {
                            IXLWorksheet communityLevelSelfAssessmentSheet = workbook.Worksheet(isDefaultlanguage ?
                                _questionBankExcelSettings.CommunityLevelSelfAssessmentSheet.Name : _questionBankExcelSettings.CommunityLevelSelfAssessmentSheet.Name_FR);

                            CreateTemplateSheet(communityLevelSelfAssessmentSheet, _questionBankExcelSettings.AssessmentSheet.SelfAssessmentSheetStartingRowNumber, Convert.ToInt32(QBRespondentType.CommunityLevel), withSelfAssesmentQuestion);
                        }
                    }

                    #endregion             

                    ModifyHealthFacilitySheet(workbook, questionDocumentInputModel.HealthFacilities, isDefaultlanguage);

                    workbook.SaveAs(memoryStream);
                }

                return memoryStream.ToArray();
            }
        }
        #endregion

        #region Private Method

        /// <summary>
        /// Remove the existing template sheet based on the respondent type of assessment.
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook from this the sheets will be removed</param>    
        /// <param name="isDefaultlanguage"> boolen value</param>
        /// <param name="respondentTypes">Remove sheets if they are not in this array</param>
        private void RemoveSheetIfNotInRespondentType(XLWorkbook workbook, int[] respondentTypes, bool isDefaultlanguage)
        {
            if (!respondentTypes.Any(id => id == Convert.ToInt32(QBRespondentType.SubnationalLevel)))
            {
                IXLWorksheet subNationalLevelWorksheet = workbook.Worksheet(isDefaultlanguage ?
                     _questionBankExcelSettings.SubNationalSheet.Name : _questionBankExcelSettings.SubNationalSheet.Name_FR);
                subNationalLevelWorksheet.Delete();

                workbook.TryGetWorksheet(isDefaultlanguage ?
                    _questionBankExcelSettings.SubNationalSelfAssessmentSheet.Name : _questionBankExcelSettings.SubNationalSelfAssessmentSheet.Name_FR
                    , out IXLWorksheet subNationalLevelSelfAssessmetWorksheet);
                if (subNationalLevelSelfAssessmetWorksheet != null)
                {
                    subNationalLevelSelfAssessmetWorksheet.Delete();
                }
            }

            if (!respondentTypes.Any(id => id == Convert.ToInt32(QBRespondentType.ServiceDeliveryLevel)))
            {
                IXLWorksheet serviceLevelWorksheet = workbook.Worksheet(isDefaultlanguage ?
                    _questionBankExcelSettings.ServiceLevelSheet.Name : _questionBankExcelSettings.ServiceLevelSheet.Name_FR);
                serviceLevelWorksheet.Delete();

                workbook.TryGetWorksheet(isDefaultlanguage ?
                    _questionBankExcelSettings.ServiceLevelSelfAssessmentSheet.Name : _questionBankExcelSettings.ServiceLevelSelfAssessmentSheet.Name_FR,
                    out IXLWorksheet serviceLevelSelfAssessmetWorksheet);
                if (serviceLevelSelfAssessmetWorksheet != null)
                {
                    serviceLevelSelfAssessmetWorksheet.Delete();
                }
            }

            if (!respondentTypes.Any(id => id == Convert.ToInt32(QBRespondentType.CommunityLevel)))
            {
                IXLWorksheet communityLevelWorksheet = workbook.Worksheet(isDefaultlanguage ?
                    _questionBankExcelSettings.CommunityLevelSheet.Name : _questionBankExcelSettings.CommunityLevelSheet.Name_FR);
                communityLevelWorksheet.Delete();

                workbook.TryGetWorksheet(isDefaultlanguage ? _questionBankExcelSettings.CommunityLevelSelfAssessmentSheet.Name :
                    _questionBankExcelSettings.CommunityLevelSelfAssessmentSheet.Name_FR, out IXLWorksheet communityLevelSelfAssessmetWorksheet);
                if (communityLevelSelfAssessmetWorksheet != null)
                {
                    communityLevelSelfAssessmetWorksheet.Delete();
                }
            }
        }

        /// <summary>
        /// Remove self-assessment sheet based on the self assessment flag.
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook from this the sheets will be removed</param> 
        /// <param name="isDefaultlanguage"> boolen value</param>
        private void RemoveSelfAssessedQuestionsSheets(XLWorkbook workbook, bool isDefaultlanguage)
        {
            IXLWorksheet subNationalLevelSelfAssessmetWorksheet = workbook.Worksheet(isDefaultlanguage ?
                _questionBankExcelSettings.SubNationalSelfAssessmentSheet.Name :
                _questionBankExcelSettings.SubNationalSelfAssessmentSheet.Name_FR);
            subNationalLevelSelfAssessmetWorksheet.Delete();

            IXLWorksheet serviceLevelSelfAssessmetWorksheet = workbook.Worksheet(isDefaultlanguage ?
                _questionBankExcelSettings.ServiceLevelSelfAssessmentSheet.Name :
                _questionBankExcelSettings.ServiceLevelSelfAssessmentSheet.Name_FR);
            serviceLevelSelfAssessmetWorksheet.Delete();

            IXLWorksheet communityLevelSelfAssessmetWorksheet = workbook.Worksheet(isDefaultlanguage ?
                _questionBankExcelSettings.CommunityLevelSelfAssessmentSheet.Name :
                _questionBankExcelSettings.CommunityLevelSelfAssessmentSheet.Name_FR);
            communityLevelSelfAssessmetWorksheet.Delete();
        }

        /// <summary>
        ///  Create a template sheet based on respondent types.
        /// </summary>
        /// <param name="workSheet">IXLWorksheet</param>
        /// <param name="startRowNumber">Starting row number of the sheet</param>
        /// <param name="respondentType">Respondent types of assessment</param>
        /// <param name="assessmentQuestionData">List of objective, sub-objective, indicator, questions</param>
        private void CreateTemplateSheet(IXLWorksheet workSheet, int startRowNumber, int respondentType, IEnumerable<Question> assessmentQuestionData)
        {
            #region Configure  Sheet        

            int firstCellRowIndex = 0;
            int lastCellRowIndex = 0;
            int firstCellCoumnIndex = 0;
            int lastCellColumnIndex = 0;

            string cellAddress = string.Empty;
            string cellValue = string.Empty;
            string color = string.Empty;

            IEnumerable<ObjectiveModel> objectives = assessmentQuestionData.Where(x => x.RespondentType == respondentType)
                                                                           .GroupBy(x => new { x.ObjectiveId, x.ObjectiveName, x.ObjectiveSequence })
                                                                           .Select(x => new ObjectiveModel
                                                                           {
                                                                               Id = x.Key.ObjectiveId,
                                                                               Name = x.Key.ObjectiveName,
                                                                               Sequence = x.Key.ObjectiveSequence
                                                                           }).ToList();

            foreach (ObjectiveModel objective in objectives)
            {
                cellAddress = $"{_questionBankExcelSettings.AssessmentSheet.ObjectiveCell.Column}{_questionBankExcelSettings.AssessmentSheet.ObjectiveCell.FirstCellRow + startRowNumber}";
                cellValue = $"{objective.Sequence} {objective.Name}";
                workSheet.Cell(cellAddress).SetValue<string>(cellValue);
                workSheet.Cell(cellAddress).Style.Alignment.WrapText = true;

                color = objective.Sequence.GetColorCodeForObjective();

                SetBackgroundColorToWorksheet(workSheet, startRowNumber, firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex, color);

                IEnumerable<SubObjectiveModel> subObjectives = assessmentQuestionData.Where(x => x.ObjectiveId == objective.Id && x.RespondentType == respondentType)
                                                                                      .GroupBy(x => new { x.SubObjectiveId, x.SubObjectiveName, x.SubObjectiveSequence })
                                                                                      .Select(x => new SubObjectiveModel
                                                                                      {
                                                                                          Id = x.Key.SubObjectiveId,
                                                                                          Name = x.Key.SubObjectiveName,
                                                                                          Sequence = x.Key.SubObjectiveSequence
                                                                                      }).ToList();

                startRowNumber++;
                foreach (SubObjectiveModel subObjective in subObjectives)
                {
                    cellAddress = $"{_questionBankExcelSettings.AssessmentSheet.SubObjectiveCell.Column}{_questionBankExcelSettings.AssessmentSheet.SubObjectiveCell.FirstCellRow + startRowNumber}";
                    cellValue = $"{subObjective.Sequence} {subObjective.Name}";

                    workSheet.Cell(cellAddress).SetValue<string>(cellValue);
                    workSheet.Cell(cellAddress).Style.Alignment.WrapText = true;

                    color = objective.Sequence.GetColorCodeForSubObjective();

                    SetBackgroundColorToWorksheet(workSheet, startRowNumber, firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex, color);


                    IEnumerable<QuestionIndicator> questions = assessmentQuestionData.Where(x => x.SubObjectiveId == subObjective.Id && x.RespondentType == respondentType)
                                                                                     .GroupBy(x => new { x.IndicatorName, x.IndicatorSequence, x.QuestionName, x.ResponseOption, x.Notes, x.Code })
                                                                                     .Select(x => new QuestionIndicator
                                                                                     {
                                                                                         IndicatorName = x.Key.IndicatorName,
                                                                                         IndicatorSequence = x.Key.IndicatorSequence,
                                                                                         Question = x.Key.QuestionName,
                                                                                         ResponseOption = x.Key.ResponseOption,
                                                                                         Notes = x.Key.Notes,
                                                                                         Code = x.Key.Code
                                                                                     });

                    startRowNumber++;

                    foreach (QuestionIndicator question in questions)
                    {

                        cellAddress = $"{_questionBankExcelSettings.AssessmentSheet.IndicatorSequenceCell.Column}{_questionBankExcelSettings.AssessmentSheet.IndicatorSequenceCell.FirstCellRow + startRowNumber}";
                        workSheet.Cell(cellAddress).SetValue<string>(question.IndicatorSequence);
                        workSheet.Cell(cellAddress).Style.Alignment.WrapText = true;

                        cellAddress = $"{_questionBankExcelSettings.AssessmentSheet.IndicatorNameCell.Column}{_questionBankExcelSettings.AssessmentSheet.IndicatorNameCell.FirstCellRow + startRowNumber}";
                        workSheet.Cell(cellAddress).SetValue<string>(question.IndicatorName);
                        workSheet.Cell(cellAddress).Style.Alignment.WrapText = true;

                        cellAddress = $"{_questionBankExcelSettings.AssessmentSheet.CodeNameCell.Column}{_questionBankExcelSettings.AssessmentSheet.CodeNameCell.FirstCellRow + startRowNumber}";
                        workSheet.Cell(cellAddress).SetValue<string>(question.Code);
                        workSheet.Cell(cellAddress).Style.Alignment.WrapText = true;

                        cellAddress = $"{_questionBankExcelSettings.AssessmentSheet.QuestionCell.Column}{_questionBankExcelSettings.AssessmentSheet.QuestionCell.FirstCellRow + startRowNumber}";
                        workSheet.Cell(cellAddress).SetValue<string>(question.Question);
                        workSheet.Cell(cellAddress).Style.Alignment.WrapText = true;

                        cellAddress = $"{_questionBankExcelSettings.AssessmentSheet.ResponseOptionCell.Column}{_questionBankExcelSettings.AssessmentSheet.ResponseOptionCell.FirstCellRow + startRowNumber}";
                        workSheet.Cell(cellAddress).SetValue<string>(question.ResponseOption);
                        workSheet.Cell(cellAddress).Style.Alignment.WrapText = true;

                        cellAddress = $"{_questionBankExcelSettings.AssessmentSheet.NotesCell.Column}{_questionBankExcelSettings.AssessmentSheet.NotesCell.FirstCellRow + startRowNumber}";
                        workSheet.Cell(cellAddress).SetValue<string>(question.Notes);
                        workSheet.Cell(cellAddress).Style.Alignment.WrapText = true;

                        startRowNumber++;
                    }
                }
            }


            #endregion
        }

        /// <summary>
        /// Verifies if all are duplicates in the given response options
        /// </summary>
        /// <param name="responseOption">Response Options</param>
        /// <param name="updatedResponse">If Response Options are duplicate, then unique Response Options list</param>
        /// <returns>true if all values are duplicates else false</returns>
        private bool VerifyDuplicatesInResponseOptions(string responseOption, ref string updatedResponse)
        {
            if (string.IsNullOrEmpty(responseOption))
                return false;

            string[] responseArray = responseOption.Split('\n');
            var isDuplicate = false;
            foreach (var responseItem in responseArray)
            {
                isDuplicate = responseArray.Count(w => w.Equals(responseItem)) > 1;
                if (!isDuplicate)
                {
                    break;
                }
            }

            if (isDuplicate)
            {
                string[] filteredResponse = new string[responseArray.Length];
                var colCount = 0;
                foreach (var responseItem in responseArray)
                {
                    if (!filteredResponse.Contains(responseItem))
                        filteredResponse[colCount] = responseItem;

                    colCount++;
                }

                foreach (var item in filteredResponse)
                {
                    updatedResponse = updatedResponse + item;
                }

                return true;
            }

            return false;
        }

        /// <summary>
        /// Set the background colour for that row cell range on the work sheet.
        /// </summary>
        /// <param name="workSheet">IXLWorksheet </param>
        /// <param name="firstCellRowIndex">First cell row index</param>
        /// <param name="firstCellCoumnIndex">First cell column index</param>
        /// <param name="lastCellRowIndex">Last cell row index</param>
        /// <param name="lastCellColumnIndex">Last cell column index</param>
        /// <param name="color">color code</param>
        private void SetBackgroundColorToWorksheet(IXLWorksheet workSheet, int excelRowCount, int firstCellRowIndex, int firstCellCoumnIndex, int lastCellRowIndex, int lastCellColumnIndex, string color)
        {
            firstCellRowIndex = excelRowCount + _questionBankExcelSettings.AssessmentSheet.ObjectiveCell.FirstCellRow;
            firstCellCoumnIndex = _questionBankExcelSettings.AssessmentSheet.ObjectiveCell.FirstCellColumn;
            lastCellRowIndex = excelRowCount + _questionBankExcelSettings.AssessmentSheet.ObjectiveCell.LastCellRow + 1;
            lastCellColumnIndex = _questionBankExcelSettings.AssessmentSheet.ObjectiveCell.LastCellColumn + 1;
            workSheet.Range(firstCellRowIndex, firstCellCoumnIndex, lastCellRowIndex, lastCellColumnIndex).Style.Fill.SetBackgroundColor(XLColor.FromHtml(color)).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
        }

        /// <summary>
        /// Generate health facilities template
        /// </summary> 
        /// <param name="healthFacilities">Must contains health facilities in a DataTable</param>/>
        /// <returns>byte[] of the modified template</returns>
        public byte[] GenerateHealthFacilitiesTemplate(DataTable healthFacilities)
        {
            using (MemoryStream memoryStream = new MemoryStream())
            {
                var workbook = new XLWorkbook();

                IXLWorksheet worksheet = workbook.Worksheets.Add(Constants.HealthFacilitySheetName);

                worksheet.Cell(1, 1).InsertTable(healthFacilities);

                worksheet.Columns(1, 7).Width = 20;

                workbook.SaveAs(memoryStream);

                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// Read health facility data from the Excel sheet
        /// </summary>
        /// <param name="file">An Excel file</param>     
        /// <returns>Collection of health facilities</returns>
        public List<HealthFacilityDto> ReadHealthFacilitySheet(IFormFile file)
        {
            List<HealthFacilityDto> healthFacilities = new List<HealthFacilityDto>();

            using (MemoryStream memorystream = new MemoryStream())
            {
                file.CopyTo(memorystream);
                {
                    using (XLWorkbook workbook = new XLWorkbook(memorystream))
                    {
                        bool IsValidSheet = workbook.TryGetWorksheet(Constants.HealthFacilitySheetName, out IXLWorksheet healthFacilityWorksheet);

                        if (!IsValidSheet)
                        {
                            throw new WrongSheetNameException("Health Facility Template", Constants.HealthFacilitySheetName, null, Constants.InvalidSheetName);
                        }

                        healthFacilityWorksheet = workbook.Worksheet(Constants.HealthFacilitySheetName);

                        //Get only filtered row data and skip 1st header column row
                        IEnumerable<IXLRow> visibleRows = healthFacilityWorksheet.RowsUsed(x => x.IsHidden == false).Skip(1);

                        visibleRows.ToList().ForEach(row =>
                        {
                            if (!string.IsNullOrEmpty(Convert.ToString(row.Cell(1).Value)))
                            {
                                HealthFacilityDto healthFacility = new HealthFacilityDto()
                                {
                                    CountryName = row.Cell(1).GetString(),
                                    Region = row.Cell(2).GetString(),
                                    DistrictCode = row.Cell(3).GetString(),
                                    DistrictName = row.Cell(4).GetString(),
                                    Code = row.Cell(5).GetString(),
                                    Name = row.Cell(6).GetString(),
                                    Type = row.Cell(7).GetString()
                                };

                                healthFacilities.Add(healthFacility);
                            }
                        });
                    }
                }

                return healthFacilities;
            }
        }

        /// <summary>
        /// Modify Health facility sheet in question bank template excel file
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>         
        /// <param name="healthFacilities">Must contains health facilities in a DataTable</param>
        /// <param name="isDefaultlanguage"> boolen value</param>
        private void ModifyHealthFacilitySheet(XLWorkbook workbook, DataTable healthFacilities, bool isDefaultlanguage)
        {
            SheetSetting excelSetting = _questionBankExcelSettings.HealthFacilitySheet;

            IXLWorksheet worksheet = workbook.Worksheet(isDefaultlanguage ? excelSetting.Name : excelSetting.Name_FR);

            worksheet.Cell(1, 1).InsertTable(healthFacilities.AsEnumerable());

            worksheet.Cells().Style.Alignment.WrapText = true;

            worksheet.Columns().AdjustToContents();
        }
        #endregion
    }
}