﻿using ClosedXML.Excel;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.DocumentManager.Exceptions;
using WHO.MALARIA.DocumentManager.Models;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.SeedingMetadata;

namespace WHO.MALARIA.DocumentManager
{
    /// <summary>
    /// Contains methods for shell table question bank document(s) to create/process documents 
    /// </summary>
    public class ShellTable : IShellTable
    {
        private readonly ShellTableExcelSetting _shellTableExcelSetting;
        private readonly ITranslationService _translationService;
        private string analysis_8 = "analysis_8";

        public ShellTable(IOptions<ShellTableExcelSetting> shellTableExcelSetting, ITranslationService translationService)
        {
            _shellTableExcelSetting = shellTableExcelSetting.Value;
            _translationService = translationService;
        }

        #region  Public Method

        /// <summary>
        /// Generate shell table template
        /// </summary>      
        /// <param name="shellTableTemplateInputModel">Shell table template input model</param>   
        /// <param name="language">language code</param>
        /// <returns>byte[] of the modified template</returns>
        public byte[] GenerateTemplate(ShellTableTemplateInputModel shellTableTemplateInputModel, string language)
        {
            bool isDeafaultLanguage = language == Common.Constants.Common.DefaultLanguage;

            using (MemoryStream memoryStream = new MemoryStream())
            {
                string templateFilePath = Path.Combine(Environment.CurrentDirectory, $"{Constants.ShellTableTemplateFilePath}_{language}.xlsx");

                using (XLWorkbook workbook = new XLWorkbook(templateFilePath))
                {
                    //Modify objective1 sheet from shell table template sheet
                    ModifyObjective_1_Sheet(workbook, shellTableTemplateInputModel.Questions, shellTableTemplateInputModel.ShellTableQuestionBankMappings, isDeafaultLanguage);

                    //Modify objective2 sheet from shell table template sheet
                    ModifyObjective_2_Sheet(workbook, shellTableTemplateInputModel.Questions, shellTableTemplateInputModel.ShellTableQuestionBankMappings, isDeafaultLanguage);

                    //Modify objective3 sheet from shell table template sheet
                    ModifyObjective_3_Sheet(workbook, shellTableTemplateInputModel.Questions, shellTableTemplateInputModel.ShellTableQuestionBankMappings, isDeafaultLanguage);

                    //Modify objective4 sheet from shell table template sheet
                    ModifyObjective_4_Sheet(workbook, shellTableTemplateInputModel.Questions, shellTableTemplateInputModel.ShellTableQuestionBankMappings, isDeafaultLanguage);

                    ModifyHealthFacilitySheet(workbook, shellTableTemplateInputModel.HealthFacilities, isDeafaultLanguage);

                    AddHiddenSheet(workbook, shellTableTemplateInputModel);

                    workbook.SaveAs(memoryStream);
                }

                return memoryStream.ToArray();
            }
        }

        /// <summary>
        /// Read shell table from the Excel sheet
        /// </summary>
        /// <param name="file">An Excel file</param> 
        /// <param name="questionDocumentInputModel">An object of QuestionDocumentInputModel</param>
        /// <returns>Shell table details</returns>
        public ShellTableDto ReadShellTableSheet(IFormFile file, QuestionDocumentInputModel questionDocumentInputModel)
        {
            ShellTableDto shellTable = new ShellTableDto();

            List<ShellTableQuestionDto> shellTableDetails = new List<ShellTableQuestionDto>();
            string translation = _translationService.GetCurrentCulture();

            using (MemoryStream memorystream = new MemoryStream())
            {
                file.CopyTo(memorystream);
                {
                    using (XLWorkbook workbook = new XLWorkbook(memorystream))
                    {
                        IEnumerable<IXLRow> healthFacilityWorksheet;
                        if (translation.ToLower() == "en")
                        {
                            healthFacilityWorksheet = workbook.Worksheet(_shellTableExcelSetting.HealthFacility.Name).RowsUsed(x => x.IsHidden == false).Skip(1);
                        }
                        else
                        {
                            healthFacilityWorksheet = workbook.Worksheet(_shellTableExcelSetting.HealthFacility.Name_FR).RowsUsed(x => x.IsHidden == false).Skip(1);
                        }

                        shellTable.NoOfRecordsInHealthFacilitySheet = healthFacilityWorksheet.Count();

                        IXLWorksheet hiddenWorksheet = workbook.Worksheet(workbook.Worksheets.Count); //Hidden Sheet

                        string healthFacilityCode = string.Empty;
                        string healthFacilityName = string.Empty;
                        string districtCode = string.Empty;
                        string districtName = string.Empty;
                        string regionName = string.Empty;
                        string healthFacilityType = string.Empty;
                        int respondentTypeValue;
                        IXLRow healthFacilityRow = null;
                        int shellTableVersion;
                        Guid templateAssessmentId;
                        int sheetNumber = 0;
                        string rowCellIndex = string.Empty;

                        int hiddentWorkSheetNumber = workbook.Worksheets.Count;

                        respondentTypeValue = Convert.ToInt32(hiddenWorksheet.Cell("A3").Value);

                        shellTable.TemplateRespondentType = respondentTypeValue;


                        if (!int.TryParse(hiddenWorksheet.Cell("A3").Value.ToString(), out respondentTypeValue))
                        {
                            throw new ExcelCellException(file.FileName, hiddentWorkSheetNumber, "A3");
                        }

                        if (!int.TryParse(hiddenWorksheet.Cell("A2").Value.ToString(), out shellTableVersion))
                        {
                            throw new ExcelCellException(file.FileName, hiddentWorkSheetNumber, "A2");
                        }

                        if (!Guid.TryParse(hiddenWorksheet.Cell("A1").Value?.ToString(), out templateAssessmentId))
                        {
                            throw new ExcelCellException(file.FileName, hiddentWorkSheetNumber, "A1");
                        }

                        shellTable.TemplateVersion = shellTableVersion;

                        shellTable.TemplateAssessementId = templateAssessmentId;

                        shellTable.InputVersion = questionDocumentInputModel.Questions.FirstOrDefault(q => q.RespondentType == respondentTypeValue).ShellTableFileVersion;

                        shellTable.InputRespondentType = questionDocumentInputModel.Questions.FirstOrDefault(q => q.RespondentType == respondentTypeValue).RespondentType;

                        if (shellTable.NoOfRecordsInHealthFacilitySheet == 0 || shellTable.NoOfRecordsInHealthFacilitySheet > 1)
                        {
                            return shellTable;
                        }

                        healthFacilityRow = healthFacilityWorksheet.FirstOrDefault();

                        healthFacilityCode = healthFacilityRow.Cell(5).GetString();
                        healthFacilityName = healthFacilityRow.Cell(6).GetString();
                        healthFacilityType = healthFacilityRow.Cell(7).GetString();

                        if (respondentTypeValue != (int)QBRespondentType.SubnationalLevel)
                        {
                            if (healthFacilityCode is null)
                            {
                                sheetNumber = Constants.ShellTable.HealthFacilitySheetNumber;
                                throw new ExcelCellException(file.FileName, sheetNumber, "F1");
                            }
                        }
                        else if (respondentTypeValue == (int)QBRespondentType.SubnationalLevel)
                        {
                            healthFacilityCode = null;
                            healthFacilityName = null;
                            healthFacilityType = null;
                        }

                        regionName = healthFacilityRow.Cell(2).GetString();
                        districtCode = healthFacilityRow.Cell(3).GetString();
                        districtName = healthFacilityRow.Cell(4).GetString();

                        if (districtCode is null)
                        {
                            sheetNumber = Constants.ShellTable.HealthFacilitySheetNumber;
                            throw new ExcelCellException(file.FileName, sheetNumber, "C1");
                        }

                        Read_Objective1_Sheet(workbook, questionDocumentInputModel, shellTableDetails, respondentTypeValue, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, translation);

                        Read_Objective2_Sheet(workbook, questionDocumentInputModel, shellTableDetails, respondentTypeValue, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, translation);

                        Read_Objective3_Sheet(workbook, questionDocumentInputModel, shellTableDetails, respondentTypeValue, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, translation);

                        Read_Objective4_Sheet(workbook, questionDocumentInputModel, shellTableDetails, respondentTypeValue, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, translation);

                        shellTable.shellTableQuestions = shellTableDetails;

                    }
                }

                return shellTable;
            }
        }

        /// <summary>
        /// Generate shell table flat file
        /// </summary> 
        /// <param name="shellTableQuestions">Must contains shell table question in a DataTable</param>/>
        /// <param name="name">Sheet name</param>/>
        /// <returns>byte[] of the shell table file uploaded data</returns>
        public byte[] GenerateShellTableFlatFile(DataTable shellTableQuestions, string name)
        {
            using (MemoryStream memoryStream = new MemoryStream())
            {
                string templateFilePath = Path.Combine(Environment.CurrentDirectory, Constants.ShellTableDataTemplateFilePath);

                using (XLWorkbook workbook = new XLWorkbook(templateFilePath))
                {
                    IXLWorksheet worksheet = workbook.Worksheet(1);

                    worksheet.Name = name;

                    worksheet.Cell(1, 1).InsertTable(shellTableQuestions.AsEnumerable());

                    IXLRange range = worksheet.Range(1, 1, shellTableQuestions.Rows.Count, shellTableQuestions.Columns.Count);

                    worksheet.Cells().Style.Alignment.WrapText = true;

                    worksheet.Columns().AdjustToContents();

                    workbook.SaveAs(memoryStream);
                }

                return memoryStream.ToArray();
            }
        }
        #endregion

        #region Private Method
        /// <summary>
        ///  Modify objective 1 work sheet
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>
        /// <param name="questions">List of questions</param>
        /// <param name="shellTableQuestionBankMappings">List of shell table questions mapping</param>
        /// <param name="isDefaultLanguage">boolen value</param>
        private void ModifyObjective_1_Sheet(XLWorkbook workbook, IEnumerable<Question> questions, IEnumerable<ShellTableQuestionBankMappingDto> shellTableQuestionBankMappings, bool isDefaultLanguage)
        {
            Objective_1_Sheet excelSetting = _shellTableExcelSetting.Objective1;

            IEnumerable<Question> objective_1_questions = questions.Where(q => q.ObjectiveSequence == excelSetting.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

            IXLWorksheet worksheet = workbook.Worksheet(isDefaultLanguage ? excelSetting.Name : excelSetting.Name_FR);

            if (objective_1_questions.Any())
            {
                worksheet.Range(excelSetting.ProtectCellRange.FirstCellRow, excelSetting.ProtectCellRange.FirstCellColumn, excelSetting.ProtectCellRange.LastCellRow, excelSetting.ProtectCellRange.LastCellColumn).Style.Protection.SetLocked(true);

                worksheet.Protect();

                IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_1_3.IndicatorSequence);

                UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, objective_1_questions);
            }
            else
            {
                workbook.Worksheet(isDefaultLanguage ? excelSetting.Name : excelSetting.Name_FR).Delete();
            }
        }

        /// <summary>
        ///  Modify objective 2 work sheet
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>
        /// <param name="questions">List of questions</param>
        /// <param name="shellTableQuestionBankMappings">List of shell table questions mapping</param>
        /// <param name="isDefaultLanguage">boolen value</param>
        private void ModifyObjective_2_Sheet(XLWorkbook workbook, IEnumerable<Question> questions, IEnumerable<ShellTableQuestionBankMappingDto> shellTableQuestionBankMappings, bool isDeafaultLanguage)
        {
            Objective_2_Sheet excelSetting = _shellTableExcelSetting.Objective2;

            IEnumerable<Question> objective_2_questions = questions.Where(q => q.ObjectiveSequence == excelSetting.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

            IXLWorksheet worksheet = workbook.Worksheet(isDeafaultLanguage ? excelSetting.Name : excelSetting.Name_FR);

            if (objective_2_questions.Any())
            {
                worksheet.Range(excelSetting.ProtectCellRange.FirstCellRow, excelSetting.ProtectCellRange.FirstCellColumn, excelSetting.ProtectCellRange.LastCellRow, excelSetting.ProtectCellRange.LastCellColumn).Style.Protection.SetLocked(true);

                worksheet.Protect();

                IEnumerable<Question> subObjective_2_1_questions = objective_2_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_2_1.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_2_1_questions.Any())
                {
                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_2_1.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_2_1_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_2_1.FirstCellRow, excelSetting.SubObjective_2_1.LastCellRow).Hide();
                }

                IEnumerable<Question> subObjective_2_2_questions = questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_2_2.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_2_2_questions.Any())
                {
                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_2_2.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_2_2_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_2_2.FirstCellRow, excelSetting.SubObjective_2_2.LastCellRow).Hide();
                }

                IEnumerable<Question> subObjective_2_3_questions = objective_2_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_2_3.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_2_3_questions.Any())
                {
                    List<string> questionMapCode = new List<string> { ShellTableQuestionBankMappingSeedingMetadata.GUIDELINES_2a, ShellTableQuestionBankMappingSeedingMetadata.GUIDELINES_2b };

                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_2_3.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_2_3_questions, questionMapCode);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_2_3.FirstCellRow, excelSetting.SubObjective_2_3.LastCellRow).Hide();
                }

                IEnumerable<Question> subObjective_2_4_questions = objective_2_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_2_4.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_2_4_questions.Any())
                {
                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_2_4.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_2_4_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_2_4.FirstCellRow, excelSetting.SubObjective_2_4.LastCellRow).Hide();
                }
            }
            else
            {
                workbook.Worksheet(isDeafaultLanguage ? excelSetting.Name : excelSetting.Name_FR).Delete();
            }
        }

        /// <summary>
        ///  Modify objective 3 work sheet
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>
        /// <param name="questions">List of questions</param>
        /// <param name="shellTableQuestionBankMappings">List of shell table question mapping</param>
        /// <param name="isDefaultLanguage">boolen value</param>
        private void ModifyObjective_3_Sheet(XLWorkbook workbook, IEnumerable<Question> questions, IEnumerable<ShellTableQuestionBankMappingDto> shellTableQuestionBankMappings, bool isDefaultLanguage)
        {
            Objective_3_Sheet excelSetting = _shellTableExcelSetting.Objective3;

            IEnumerable<Question> objective_3_questions = questions.Where(q => q.ObjectiveSequence == excelSetting.IndicatorSequence).OrderBy(q => q.IndicatorSequence).ToList();
            var respondentType = objective_3_questions.FirstOrDefault().RespondentType;
            var isServiceDeliveryLevel = respondentType == (int)QBRespondentType.ServiceDeliveryLevel ? true : false;

            IXLWorksheet worksheet = workbook.Worksheet(isDefaultLanguage ? excelSetting.Name : excelSetting.Name_FR);

            if (objective_3_questions.Any())
            {
                worksheet.Range(excelSetting.ProtectCellRange.FirstCellRow, excelSetting.ProtectCellRange.FirstCellColumn, excelSetting.ProtectCellRange.LastCellRow, excelSetting.ProtectCellRange.LastCellColumn).Style.Protection.SetLocked(true);

                worksheet.Protect();

                IEnumerable<Question> subObjective_3_1_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_1.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_1_questions.Any())
                {
                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_1.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_3_1_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_3_1.FirstCellRow, excelSetting.SubObjective_3_1.LastCellRow).Hide();
                }

                IEnumerable<Question> subObjective_3_2_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_2.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_2_questions.Any())
                {
                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_2.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_3_2_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_3_2.FirstCellRow, excelSetting.SubObjective_3_2.LastCellRow).Hide();
                }

                IEnumerable<Question> subObjective_3_3_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_3.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_3_questions.Any())
                {
                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_3.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_3_3_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_3_3.FirstCellRow, excelSetting.SubObjective_3_3.LastCellRow).Hide();
                }

                IEnumerable<Question> subObjective_3_4_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_4.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_4_questions.Any())
                {
                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_4.IndicatorSequence).ToList();

                    if (isServiceDeliveryLevel)
                    {
                        ShellTableQuestionBankMappingDto indicatorSetting = indicatorSettings.Where(q => q.QuestionBankQuestionCode.Equals(analysis_8)).FirstOrDefault();
                        worksheet.Rows(indicatorSetting.IndicatorMapSetting.Options.FirstCellRow + 1, indicatorSetting.IndicatorMapSetting.Options.LastCellRow).Hide();

                        worksheet.Rows(excelSetting.SubObjective_3_4_3.FirstCellRow, excelSetting.SubObjective_3_4_3.LastCellRow).Hide();
                        IXLCell cell = worksheet.Cell(excelSetting.SubObjective_3_4_3.FirstCellColumn, excelSetting.SubObjective_3_4_3.LastCellColumn);
                        cell.Style.Protection.SetLocked(false);
                        cell.SetValue<string>(analysis_8);
                        cell.Style.Protection.SetLocked(true);
                    }

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_3_4_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_3_4.FirstCellRow, excelSetting.SubObjective_3_4.LastCellRow).Hide();
                }

                IEnumerable<Question> subObjective_3_5_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_5.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_5_questions.Any())
                {
                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_5.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_3_5_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_3_5.FirstCellRow, excelSetting.SubObjective_3_5.LastCellRow).Hide();
                }

                IEnumerable<Question> subObjective_3_6_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_6.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_6_questions.Any())
                {
                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_6.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_3_6_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_3_6.FirstCellRow, excelSetting.SubObjective_3_6.LastCellRow).Hide();
                }
            }
            else
            {
                workbook.Worksheet(isDefaultLanguage ? excelSetting.Name : excelSetting.Name_FR).Delete();
            }
        }

        /// <summary>
        ///  Modify objective 4 work sheet
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>
        /// <param name="questions">List of questions</param>
        /// <param name="shellTableQuestionBankMappings">List of shell table questions mapping</param>
        /// <param name="isDefaultLanguage">boolen value</param>
        private void ModifyObjective_4_Sheet(XLWorkbook workbook, IEnumerable<Question> questions, IEnumerable<ShellTableQuestionBankMappingDto> shellTableQuestionBankMappings, bool isDefaultLanguage)
        {
            Objective_4_Sheet excelSetting = _shellTableExcelSetting.Objective4;

            IEnumerable<Question> objective_4_questions = questions.Where(q => q.ObjectiveSequence == excelSetting.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

            IXLWorksheet worksheet = workbook.Worksheet(isDefaultLanguage ? excelSetting.Name : excelSetting.Name_FR);

            if (objective_4_questions.Any())
            {
                worksheet.Range(excelSetting.ProtectCellRange.FirstCellRow, excelSetting.ProtectCellRange.FirstCellColumn, excelSetting.ProtectCellRange.LastCellRow, excelSetting.ProtectCellRange.LastCellColumn).Style.Protection.SetLocked(true);

                worksheet.Protect();

                IEnumerable<Question> subObjective_4_1_questions = objective_4_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_4_1.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_4_1_questions.Any())
                {

                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_4_1.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_4_1_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_4_1.FirstCellRow, excelSetting.SubObjective_4_1.LastCellRow).Hide();
                }


                IEnumerable<Question> subObjective_4_2_questions = objective_4_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_4_2.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_4_2_questions.Any())
                {
                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_4_2.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_4_2_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_4_2.FirstCellRow, excelSetting.SubObjective_4_2.LastCellRow).Hide();
                }

                IEnumerable<Question> subObjective_4_3_questions = objective_4_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_4_3.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_4_3_questions.Any())
                {
                    List<string> shellTableQuestionCodes = new List<string> { ShellTableQuestionBankMappingSeedingMetadata.SUPERVISION_8a, ShellTableQuestionBankMappingSeedingMetadata.SUPERVISION_8b };

                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_4_3.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_4_3_questions, shellTableQuestionCodes);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_4_3.FirstCellRow, excelSetting.SubObjective_4_3.LastCellRow).Hide();
                }

                IEnumerable<Question> subObjective_4_4_questions = objective_4_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_4_4.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_4_4_questions.Any())
                {
                    IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(shellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_4_4.IndicatorSequence);

                    UnlockOrHideSheetCellsRange(worksheet, indicatorSettings, subObjective_4_4_questions);
                }
                else
                {
                    worksheet.Rows(excelSetting.SubObjective_4_4.FirstCellRow, excelSetting.SubObjective_4_4.LastCellRow).Hide();
                }
            }
            else
            {
                workbook.Worksheet(isDefaultLanguage ? excelSetting.Name : excelSetting.Name_FR).Delete();
            }
        }

        /// <summary>
        /// Modify Health facility sheet in shell table template excel file
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>         
        /// <param name="healthFacilities">Must contains health facilities in a DataTable</param>
        /// <param name="isDefaultLanguage">boolen value</param>
        private void ModifyHealthFacilitySheet(XLWorkbook workbook, DataTable healthFacilities, bool isDefaultLanguage)
        {
            HealthFacilitySheet excelSetting = _shellTableExcelSetting.HealthFacility;

            IXLWorksheet worksheet = workbook.Worksheet(isDefaultLanguage ? excelSetting.Name : excelSetting.Name_FR);

            // translate header columns it may be 6 to 7 column
            foreach (DataColumn column in healthFacilities.Columns)
            {
                column.ColumnName = _translationService.GetTranslation(column.ColumnName);
            }

            worksheet.Cell(1, 1).InsertTable(healthFacilities.AsEnumerable());

            worksheet.Cells().Style.Alignment.WrapText = true;

            worksheet.Columns().AdjustToContents();
        }

        /// <summary>
        ///  Creating additional hidden sheet which hold the assessmentId, version, respondent type so that it can be validated against the uploaded file
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>
        /// <param name="shellTableTemplateInputModel">Shell table input model</param>
        private void AddHiddenSheet(XLWorkbook workbook, ShellTableTemplateInputModel shellTableTemplateInputModel)
        {
            IXLWorksheet hiddenWorksheet = workbook.Worksheets.Add(workbook.Worksheets.Count + 1);
            hiddenWorksheet.Cell("A1").SetValue<Guid>(shellTableTemplateInputModel.AssessmentId).Style.Protection.SetHidden().Protection.SetLocked(true);
            hiddenWorksheet.Cell("A2").SetValue<int>(shellTableTemplateInputModel.ShellTableFileVersion).Style.Protection.SetHidden().Protection.SetLocked(true);
            hiddenWorksheet.Cell("A3").SetValue<int>(shellTableTemplateInputModel.RespondentType).Style.Protection.SetHidden().Protection.SetLocked(true);
            hiddenWorksheet.Hide().Protect();
        }

        /// <summary>
        /// Read objective1 data from shell table excel
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>
        /// <param name="questionDocumentInputModel">An object of QuestionDocumentInputModel</param>
        /// <param name="shellTableDetails">List of  ShellTableDetailsDto</param>
        /// <param name="respondentTypeValue">Respondent Type</param>       
        /// <param name="healthFacilityCode">Health facility code</param>
        /// <param name="districtCode">District code</param>
        /// <param name="regionName">Region name</param>
        /// <param name="districtName">District name</param>
        /// <param name="healthFacilityName">Health facility name</param>
        /// <param name="healthFacilityType">Health facility type</param>  
        /// <returns>List of shell tables details data</returns>
        private List<ShellTableQuestionDto> Read_Objective1_Sheet(XLWorkbook workbook, QuestionDocumentInputModel questionDocumentInputModel, List<ShellTableQuestionDto> shellTableDetails, int respondentTypeValue, string healthFacilityCode, string districtCode, string regionName, string districtName, string healthFacilityName, string healthFacilityType, string translation)
        {
            Objective_1_Sheet excelSetting = _shellTableExcelSetting.Objective1;

            IEnumerable<Question> objective_1_questions = questionDocumentInputModel.Questions.Where(q => q.ObjectiveSequence == excelSetting.IndicatorSequence && q.RespondentType == respondentTypeValue).OrderBy(q => q.IndicatorSequence);

            if (objective_1_questions.Any())
            {
                if (translation.ToLower() == "en")
                {
                    IXLWorksheet worksheet = workbook.Worksheet(excelSetting.Name);

                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_1_3.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, objective_1_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }
                else
                {
                    IXLWorksheet worksheet = workbook.Worksheet(excelSetting.Name_FR);

                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_1_3.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, objective_1_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }
            }

            return shellTableDetails;
        }

        /// <summary>
        /// Read objective2 data from shell table excel
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>
        /// <param name="questionDocumentInputModel">An object of QuestionDocumentInputModel</param>
        /// <param name="shellTableDetails">List of  ShellTableDetailsDto</param>
        /// <param name="respondentTypeValue">Respondent Type</param>
        /// <param name="healthFacilityCode">Health facility code</param>
        /// <param name="districtCode">District code</param>
        /// <param name="regionName">Region name</param>
        /// <param name="districtName">District name</param>
        /// <param name="healthFacilityName">Health facility name</param>
        /// <param name="healthFacilityType">Health facility type</param>  
        /// <param name="districtCode">District code</param>
        /// <returns>List of shell tables details data</returns>
        private List<ShellTableQuestionDto> Read_Objective2_Sheet(XLWorkbook workbook, QuestionDocumentInputModel questionDocumentInputModel, List<ShellTableQuestionDto> shellTableDetails, int respondentTypeValue, string healthFacilityCode, string districtCode, string regionName, string districtName, string healthFacilityName, string healthFacilityType, string translation)
        {
            Objective_2_Sheet excelSetting = _shellTableExcelSetting.Objective2;

            IEnumerable<Question> objective_2_questions = questionDocumentInputModel.Questions.Where(q => q.ObjectiveSequence == excelSetting.IndicatorSequence && q.RespondentType == respondentTypeValue).OrderBy(q => q.IndicatorSequence);

            IXLWorksheet worksheet = null;

            if (objective_2_questions.Any())
            {
                if (translation.ToLower() == "en")
                {
                    worksheet = workbook.Worksheet(excelSetting.Name);
                }
                else
                {
                    worksheet = workbook.Worksheet(excelSetting.Name_FR);
                }

                //Fetch all questions of subobjective 2_1
                IEnumerable<Question> subObjective_2_1_questions = objective_2_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_2_1.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_2_1_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_2_1.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_2_1_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }

                //Fetch all questions of subobjective 2_2
                IEnumerable<Question> subObjective_2_2_questions = objective_2_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_2_2.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_2_2_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_2_2.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_2_2_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }

                //Fetch all questions of subobjective 2_3
                IEnumerable<Question> subObjective_2_3_questions = objective_2_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_2_3.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_2_3_questions.Any())
                {
                    List<string> shellTableQuestionCodes = new List<string> { ShellTableQuestionBankMappingSeedingMetadata.GUIDELINES_2a, ShellTableQuestionBankMappingSeedingMetadata.GUIDELINES_2b };

                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_2_3.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_2_3_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails, shellTableQuestionCodes);
                }

                //Fetch all questions of subobjective 2_4
                IEnumerable<Question> subObjective_2_4_questions = objective_2_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_2_4.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_2_4_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_2_4.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_2_4_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }
            }

            return shellTableDetails;
        }

        /// <summary>
        /// Read objective3 data from shell table excel
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>
        /// <param name="questionDocumentInputModel">An object of QuestionDocumentInputModel</param>
        /// <param name="shellTableDetails">List of  ShellTableDetailsDto</param>
        /// <param name="respondentTypeValue">Respondent Type</param>   
        /// <param name="healthFacilityCode">Health facility code</param>
        /// <param name="districtCode">District code</param>
        /// <param name="regionName">Region name</param>
        /// <param name="districtName">District name</param>
        /// <param name="healthFacilityName">Health facility name</param>
        /// <param name="healthFacilityType">Health facility type</param>  
        /// <returns>List of shell tables details data</returns>
        private List<ShellTableQuestionDto> Read_Objective3_Sheet(XLWorkbook workbook, QuestionDocumentInputModel questionDocumentInputModel, List<ShellTableQuestionDto> shellTableDetails, int respondentTypeValue, string healthFacilityCode, string districtCode, string regionName, string districtName, string healthFacilityName, string healthFacilityType, string translation)
        {
            Objective_3_Sheet excelSetting = _shellTableExcelSetting.Objective3;

            IEnumerable<Question> objective_3_questions = questionDocumentInputModel.Questions.Where(q => q.ObjectiveSequence == excelSetting.IndicatorSequence && q.RespondentType == respondentTypeValue).OrderBy(q => q.IndicatorSequence);
            IXLWorksheet worksheet = null;
            if (objective_3_questions.Any())
            {
                if (translation.ToLower() == "en")
                {
                    worksheet = workbook.Worksheet(excelSetting.Name);
                }
                else
                {
                    worksheet = workbook.Worksheet(excelSetting.Name_FR);
                }

                //Fetch all questions of subobjective 3_1
                IEnumerable<Question> subObjective_3_1_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_1.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_1_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_1.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_3_1_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }

                //Fetch all questions of subobjective 3_2
                IEnumerable<Question> subObjective_3_2_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_2.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_2_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_2.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_3_2_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }

                //Fetch all questions of subobjective 3_3
                IEnumerable<Question> subObjective_3_3_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_3.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_3_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_3.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_3_3_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }

                //Fetch all questions of subobjective 3_4
                IEnumerable<Question> subObjective_3_4_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_4.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_4_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_4.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_3_4_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }

                //Fetch all questions of subobjective 3_5
                IEnumerable<Question> subObjective_3_5_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_5.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_5_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_5.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_3_5_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }

                //Fetch all questions of subobjective 3_6
                IEnumerable<Question> subObjective_3_6_questions = objective_3_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_3_6.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_3_6_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_3_6.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_3_6_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }
            }

            return shellTableDetails;
        }

        /// <summary>
        /// Read objective4 data from shell table excel
        /// </summary>
        /// <param name="workbook">An object of XLWorkbook</param>
        /// <param name="questionDocumentInputModel">An object of QuestionDocumentInputModel</param>
        /// <param name="shellTableDetails">List of  ShellTableDetailsDto</param>
        /// <param name="respondentTypeValue">Respondent Type</param>
        /// <param name="healthFacilityCode">Health facility code</param>
        /// <param name="districtCode">District code</param>
        /// <param name="regionName">Region name</param>
        /// <param name="districtName">District name</param>
        /// <param name="healthFacilityName">Health facility name</param>
        /// <param name="healthFacilityType">Health facility type</param>  
        /// <returns>List of shell tables details data</returns>
        private List<ShellTableQuestionDto> Read_Objective4_Sheet(XLWorkbook workbook, QuestionDocumentInputModel questionDocumentInputModel, List<ShellTableQuestionDto> shellTableDetails, int respondentTypeValue, string healthFacilityCode, string districtCode, string regionName, string districtName, string healthFacilityName, string healthFacilityType, string translation)
        {
            Objective_4_Sheet excelSetting = _shellTableExcelSetting.Objective4;
            IXLWorksheet worksheet = null;
            IEnumerable<Question> objective_4_questions = questionDocumentInputModel.Questions.Where(q => q.ObjectiveSequence == excelSetting.IndicatorSequence && q.RespondentType == respondentTypeValue).OrderBy(q => q.IndicatorSequence);

            if (objective_4_questions.Any())
            {
                if (translation.ToLower() == "en")
                {
                    worksheet = workbook.Worksheet(excelSetting.Name);
                }
                else
                {
                    worksheet = workbook.Worksheet(excelSetting.Name_FR);
                }

                //Fetch all questions of subobjective 4_1
                IEnumerable<Question> subObjective_4_1_questions = objective_4_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_4_1.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_4_1_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_4_1.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_4_1_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }

                //Fetch all questions of subobjective 4_2
                IEnumerable<Question> subObjective_4_2_questions = objective_4_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_4_2.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_4_2_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_4_2.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_4_2_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }

                //Fetch all questions of subobjective 4_3
                IEnumerable<Question> subObjective_4_3_questions = objective_4_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_4_3.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_4_3_questions.Any())
                {
                    List<string> shellTableQuestionCodes = new List<string> { ShellTableQuestionBankMappingSeedingMetadata.SUPERVISION_8a, ShellTableQuestionBankMappingSeedingMetadata.SUPERVISION_8b };

                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_4_3.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_4_3_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails, shellTableQuestionCodes);
                }

                //Fetch all questions of subobjective 4_4
                IEnumerable<Question> subObjective_4_4_questions = objective_4_questions.Where(q => q.SubObjectiveSequence == excelSetting.SubObjective_4_4.IndicatorSequence).OrderBy(q => q.IndicatorSequence);

                if (subObjective_4_4_questions.Any())
                {
                    List<ShellTableQuestionBankMappingDto> indicatorSettings = GetQuestionMappingSetting(questionDocumentInputModel.ShellTableQuestionBankMappings, excelSetting.IndicatorSequence, excelSetting.SubObjective_4_4.IndicatorSequence);

                    shellTableDetails = InsertShellTableDataIntoList(worksheet, indicatorSettings, subObjective_4_4_questions, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, shellTableDetails);
                }
            }

            return shellTableDetails;
        }

        /// <summary>
        /// Get question mapping settings
        /// </summary>
        /// <param name="shellTableQuestionBankMappings">List of question mapping settings</param>
        /// <param name="objectiveSequence">Objective sequence number</param>
        /// <param name="subObjectiveSequence">Sub objective sequence number</param>
        /// <returns>List of question mapping settings</returns>
        private List<ShellTableQuestionBankMappingDto> GetQuestionMappingSetting(IEnumerable<ShellTableQuestionBankMappingDto> shellTableQuestionBankMappings, string objectiveSequence, string subObjectiveSequence)
        {
            List<ShellTableQuestionBankMappingDto> questionMapSettings = new List<ShellTableQuestionBankMappingDto>();

            shellTableQuestionBankMappings.ToList().ForEach(questionMapping =>
            {
                if (questionMapping.IndicatorMapSetting.ObjectiveSequence == objectiveSequence && questionMapping.IndicatorMapSetting.SubObjectiveSequence == subObjectiveSequence)
                {
                    questionMapSettings.Add(questionMapping);
                }
            });

            return questionMapSettings;
        }

        /// <summary>
        /// Unlock or hide sheet cell range
        /// </summary>
        /// <param name="worksheet">An object of IXLWorksheet</param>
        /// <param name="indicatorSettings">List of indicator settings</param>
        /// <param name="questions">List of questions</param>
        /// <param name="distinctQuestionMapCodes">List of distinct question map code like SUPERVISION_8a, SUPERVISION_8b</param>
        private void UnlockOrHideSheetCellsRange(IXLWorksheet worksheet, IEnumerable<ShellTableQuestionBankMappingDto> indicatorSettings, IEnumerable<Question> questions, List<string> distinctQuestionMapCodes = null)
        {
            List<string> shellTableQuestionCodes = indicatorSettings.GroupBy(x => x.ShellTableQuestionCode).Select(x => x.Key).ToList();

            shellTableQuestionCodes.ForEach((shellTableQuestionCode) =>
            {
                ShellTableQuestionBankMappingDto indicatorSetting = indicatorSettings.Where(q => q.ShellTableQuestionCode == shellTableQuestionCode).FirstOrDefault();

                shellTableQuestionCode = Regex.Replace(shellTableQuestionCode, @"\s", "");

                string[] questionCode = shellTableQuestionCode.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);

                Question question = null;

                // Check shell table questions for codes like GUIDELINES_2a, GUIDELINES_2b, SUPERVISION_8a, SUPERVISION_8b that aren't in the question bank and get question details for that question bank code
                if (distinctQuestionMapCodes != null && distinctQuestionMapCodes.IndexOf(shellTableQuestionCode) != -1)
                {
                    question = questions.FirstOrDefault(q => q.IndicatorSequence == indicatorSetting.IndicatorMapSetting.IndicatorSequence && q.Code == indicatorSetting.QuestionBankQuestionCode);
                }
                else
                {
                    question = questions.FirstOrDefault(q => q.IndicatorSequence == indicatorSetting.IndicatorMapSetting.IndicatorSequence && Array.IndexOf(questionCode, q.Code) != -1);
                }

                if (question != null)
                {
                    int startCellColumn = indicatorSetting.IndicatorMapSetting.Options.BothUnlock ? indicatorSetting.IndicatorMapSetting.Options.FirstCellColumn - 1 : indicatorSetting.IndicatorMapSetting.Options.FirstCellColumn;

                    worksheet.Range(indicatorSetting.IndicatorMapSetting.Options.FirstCellRow, startCellColumn, indicatorSetting.IndicatorMapSetting.Options.LastCellRow, indicatorSetting.IndicatorMapSetting.Options.LastCellColumn).Style.Protection.SetLocked(false);

                    // If a question has a response option from a question bank response
                    if (indicatorSetting.IndicatorMapSetting.HasResponseFromDeskReview)
                    {
                        // If the question has a parent question, then take the parent question response option
                        if (indicatorSetting.IndicatorMapSetting.IsParent)
                        {
                            question = questions.FirstOrDefault(q => q.Code == indicatorSetting.IndicatorMapSetting.ResponseOptions.ParentCode);
                        }

                        if (!string.IsNullOrEmpty(question.ResponseOption))
                        {
                            string[] responseOptions = question.ResponseOption.Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);

                            // Change the question option based on the response option in the question bank
                            if (responseOptions.Any())
                            {
                                IXLCells optionColumns = worksheet.Column(indicatorSetting.IndicatorMapSetting.ResponseOptions.FirstCellColumn).Cells(indicatorSetting.IndicatorMapSetting.ResponseOptions.FirstCellRow, indicatorSetting.IndicatorMapSetting.ResponseOptions.LastCellRow);

                                int optionColumnIndex = 0;

                                foreach (IXLCell optionColumn in optionColumns)
                                {
                                    if (optionColumnIndex <= responseOptions.Length - 1)
                                    {
                                        string optionValue = Convert.ToString(worksheet.Cell(optionColumn.Address).CachedValue);

                                        int position = optionValue.IndexOf(indicatorSetting.IndicatorMapSetting.ResponseOptions.CharacterToRemoveFromOption);

                                        if (position < 0)
                                            position = 0;

                                        string responseValue = optionValue.Remove(position);

                                        string responseOptionValue = $"{responseValue}-{responseOptions[optionColumnIndex]}";

                                        worksheet.Cell(optionColumn.Address).SetValue<string>(Convert.ToString(responseOptionValue)).Style.Protection.SetHidden().Protection.SetLocked(true).Border.SetOutsideBorder(XLBorderStyleValues.Thin);
                                    }

                                    if (optionColumnIndex == responseOptions.Length - 1)
                                    {
                                        break;
                                    }

                                    optionColumnIndex++;
                                }

                                // Hide remaining question response options row
                                int hideRowCount = (indicatorSetting.IndicatorMapSetting.ResponseOptions.LastCellRow - indicatorSetting.IndicatorMapSetting.ResponseOptions.FirstCellRow) + 1 - responseOptions.Length;

                                if (hideRowCount > 0)
                                {
                                    worksheet.Rows(indicatorSetting.IndicatorMapSetting.ResponseOptions.FirstCellRow + responseOptions.Length, indicatorSetting.IndicatorMapSetting.ResponseOptions.LastCellRow).Hide();
                                }
                            }
                        }
                        else
                        {
                            // If both option row and response option row are same then hide the entire question else hide only specific row
                            if (indicatorSetting.IndicatorMapSetting.Options.LastCellRow == indicatorSetting.IndicatorMapSetting.ResponseOptions.LastCellRow)
                            {
                                worksheet.Rows(indicatorSetting.IndicatorMapSetting.FirstCellRow, indicatorSetting.IndicatorMapSetting.LastCellRow).Hide();
                            }
                            else
                            {
                                worksheet.Rows(indicatorSetting.IndicatorMapSetting.ResponseOptions.FirstCellRow, indicatorSetting.IndicatorMapSetting.ResponseOptions.LastCellRow).Hide();
                            }
                        }
                    }
                }
                else
                {
                    worksheet.Rows(indicatorSetting.IndicatorMapSetting.FirstCellRow, indicatorSetting.IndicatorMapSetting.LastCellRow).Hide();
                }
            });
        }

        /// <summary>
        /// Get list of shell table
        /// </summary>
        /// <param name="worksheet">An object of IXLWorksheet</param>
        /// <param name="indicatorSettings">List of indicator settings</param>
        /// <param name="questions">List of questions</param>
        /// <param name="healthFacilityCode">Health facility code</param>
        /// <param name="districtCode">District code</param>
        /// <param name="regionName">Region name</param>
        /// <param name="districtName">District name</param>
        /// <param name="healthFacilityName">Health facility name</param>
        /// <param name="healthFacilityType">Health facility type</param>  
        /// <param name="shellTableDetails">List of shell tables data</param>
        /// <param name="questionCodes">List of shell table question codes like SUPERVISION_8a, SUPERVISION_8b</param>
        /// <returns>List of shell tables details data</returns>
        private List<ShellTableQuestionDto> InsertShellTableDataIntoList(IXLWorksheet worksheet, List<ShellTableQuestionBankMappingDto> indicatorSettings, IEnumerable<Question> questions, string healthFacilityCode, string districtCode, string regionName, string districtName, string healthFacilityName, string healthFacilityType, List<ShellTableQuestionDto> shellTableDetails, List<string> questionCodes = null)
        {
            List<string> shellTableQuestionCodes = indicatorSettings.GroupBy(x => x.ShellTableQuestionCode).Select(x => x.Key).ToList();

            List<ShellTableQuestionDto> shellTableQuestionsDetails = new List<ShellTableQuestionDto>();

            shellTableQuestionCodes.ForEach((shellTableQuestionCode) =>
            {
                ShellTableQuestionBankMappingDto indicatorSetting = indicatorSettings.Where(q => q.ShellTableQuestionCode == shellTableQuestionCode).FirstOrDefault();

                shellTableQuestionCode = Regex.Replace(shellTableQuestionCode, @"\s", "");

                string[] questionCode = shellTableQuestionCode.Split(new string[] { "," }, StringSplitOptions.RemoveEmptyEntries);

                Question question = null;

                // Check shell table questions for codes like GUIDELINES_2a, GUIDELINES_2b, SUPERVISION_8a, SUPERVISION_8b that aren't in the question bank and get question details for that question bank code
                if (questionCodes != null && questionCodes.IndexOf(shellTableQuestionCode) != -1)
                {
                    question = questions.FirstOrDefault(q => q.IndicatorSequence == indicatorSetting.IndicatorMapSetting.IndicatorSequence && q.Code == indicatorSetting.QuestionBankQuestionCode);
                }
                else
                {
                    question = questions.FirstOrDefault(q => q.IndicatorSequence == indicatorSetting.IndicatorMapSetting.IndicatorSequence && Array.IndexOf(questionCode, q.Code) != -1);
                }

                if (question != null)
                {
                    // If a question has a response option from a question bank response
                    if (indicatorSetting.IndicatorMapSetting.HasResponseFromDeskReview)
                    {
                        int startCellRow = 0;

                        int lastCellRow = 0;

                        int responseOptionsCount = 0;

                        Question actualQuestion = question;

                        // If the question has a parent question, then take the parent question response option
                        if (indicatorSetting.IndicatorMapSetting.IsParent)
                        {
                            question = questions.FirstOrDefault(q => q.Code == indicatorSetting.IndicatorMapSetting.ResponseOptions.ParentCode);
                        }

                        int rowCount = (indicatorSetting.IndicatorMapSetting.ResponseOptions.LastCellRow - indicatorSetting.IndicatorMapSetting.ResponseOptions.FirstCellRow) + 1;

                        startCellRow = indicatorSetting.IndicatorMapSetting.ResponseOptions.FirstCellRow;

                        lastCellRow = indicatorSetting.IndicatorMapSetting.ResponseOptions.LastCellRow;

                        if (!string.IsNullOrEmpty(question.ResponseOption))
                        {
                            string[] responseOptions = question.ResponseOption.Split(new string[] { "\n" }, StringSplitOptions.RemoveEmptyEntries);

                            // If a question has a response value, then we can get only that option's related details
                            if (responseOptions.Any())
                            {
                                responseOptionsCount = responseOptions.Length;

                                if (responseOptionsCount != rowCount && responseOptionsCount < rowCount)
                                {
                                    lastCellRow = lastCellRow - (rowCount - responseOptionsCount);
                                }

                                shellTableQuestionsDetails = GetShellTableQuestionDetails(worksheet, actualQuestion, indicatorSetting.ShellTableQuestionBankMappingId, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, indicatorSetting.IndicatorMapSetting.Options.FirstCellColumn, startCellRow, lastCellRow, 1);

                                shellTableDetails.AddRange(shellTableQuestionsDetails);
                            }
                        }

                        // If a question has a response value, then we can get only that option's related details that don't have a desk review response  
                        if (indicatorSetting.IndicatorMapSetting.ResponseOptions.LastCellRow != indicatorSetting.IndicatorMapSetting.Options.LastCellRow)
                        {
                            startCellRow = indicatorSetting.IndicatorMapSetting.ResponseOptions.LastCellRow + 1;

                            lastCellRow = indicatorSetting.IndicatorMapSetting.Options.LastCellRow;

                            shellTableQuestionsDetails = GetShellTableQuestionDetails(worksheet, actualQuestion, indicatorSetting.ShellTableQuestionBankMappingId, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, indicatorSetting.IndicatorMapSetting.Options.FirstCellColumn, startCellRow, lastCellRow, (byte)(responseOptionsCount + 1));

                            shellTableDetails.AddRange(shellTableQuestionsDetails);
                        }
                    }
                    else
                    {
                        // If a question doesn't have desk review response data, then we can read directly that question's options and value
                        shellTableQuestionsDetails = GetShellTableQuestionDetails(worksheet, question, indicatorSetting.ShellTableQuestionBankMappingId, healthFacilityCode, districtCode, regionName, districtName, healthFacilityName, healthFacilityType, indicatorSetting.IndicatorMapSetting.Options.FirstCellColumn, indicatorSetting.IndicatorMapSetting.Options.FirstCellRow, indicatorSetting.IndicatorMapSetting.Options.LastCellRow, 1);

                        shellTableDetails.AddRange(shellTableQuestionsDetails);
                    }
                }
            });

            return shellTableDetails;
        }

        /// <summary>
        ///  Get shell table questions details
        /// </summary>
        /// <param name="worksheet">An object of IXLWorksheet</param>
        /// <param name="question">Question detail</param>
        /// <param name="questionMapId">Shell table question map id</param>
        /// <param name="healthFacilityCode">Health facility code</param>
        /// <param name="districtCode">District code</param>
        /// <param name="regionName">Region name</param>
        /// <param name="districtName">District name</param>
        /// <param name="healthFacilityName">Health facility name</param>
        /// <param name="healthFacilityType">Health facility type</param>  
        /// <param name="firstCellColumn">Column of excel that want to read</param>
        /// <param name="firstCellRow">Begin the row of that template that want to start from reading</param>
        /// <param name="lastCellRow">Last row of that template that want to end from reading</param>
        /// <param name="optionOrder">Option ordering begins with that value</param>
        /// <returns>List of shell tables questions details data</returns>
        private List<ShellTableQuestionDto> GetShellTableQuestionDetails(IXLWorksheet worksheet, Question question, int questionMapId, string healthFacilityCode, string districtCode, string regionName, string districtName, string healthFacilityName, string healthFacilityType, int firstCellColumn, int firstCellRow, int lastCellRow, byte optionOrder)
        {
            List<ShellTableQuestionDto> shellTableDetails = new List<ShellTableQuestionDto>();

            IXLCells optionColumns = worksheet.Column(firstCellColumn - 1).Cells(firstCellRow, lastCellRow);

            IXLCells optionColumnsValues = worksheet.Column(firstCellColumn).Cells(firstCellRow, lastCellRow);

            int optionValueIndex = 0;

            foreach (IXLCell optionColumnSingle in optionColumns)
            {
                IXLCell[] optionValuesArray = optionColumnsValues.ToArray();

                ShellTableQuestionDto shellTableQuestion = new ShellTableQuestionDto();

                shellTableQuestion.HealthFacilityCode = healthFacilityCode;

                shellTableQuestion.DistrictCode = districtCode;

                shellTableQuestion.DistrictName = districtName;

                shellTableQuestion.RegionName = regionName;

                shellTableQuestion.HealthFacilityName = healthFacilityName;

                shellTableQuestion.HealthFacilityType = healthFacilityType;

                shellTableQuestion.Option = optionColumnSingle.CachedValue.ToString();

                shellTableQuestion.RespondentType = (Guid)question.AssessmentRespondentTypeId;

                shellTableQuestion.ShellTableQuestionBankMappingId = questionMapId;

                shellTableQuestion.OptionOrder = optionOrder;

                string optionColumnValue = (optionValuesArray[optionValueIndex].CachedValue).ToString();

                optionValueIndex++;
                optionOrder++;

                if (int.TryParse(optionColumnValue, out int optionValue))
                {
                    shellTableQuestion.Value = optionValue;
                }

                shellTableDetails.Add(shellTableQuestion);
            }
            return shellTableDetails;
        }
        #endregion
    }
}
