﻿using System.IO;

namespace WHO.MALARIA.DocumentManager
{
    class Constants
    {
        public static class Color
        {
            public const string Blue = "#4F81BC";
            public const string LightBlue = "#DAE1F3";
            public const string Green = "#339933";
            public const string LightGreen = "#E2EFDB";
            public const string Violet = "#7030A0";
            public const string LightViolet = "#DBC2EC";
            public const string Red = "#BF0000";
            public const string LightRed = "#FFD8D9";
        }
        public static class ShellTable
        {
            public const short HealthFacilitySheetNumber = 3;
        }

        public static readonly string QuestionBankTemplateFilePath = Path.Combine("Templates", "QuestionBank", "QuestionBankTemplate");

        public static readonly string AnalyticalOutputTemplateFilePath = Path.Combine("Templates", "AnalyticalOutput", "AnalyticalOutputTemplate");

        public static readonly string ShellTableTemplateFilePath = Path.Combine("Templates", "ShellTable", "ShellTableTemplate");

        public static readonly string HealthFacilityTemplateFilePath = Path.Combine("Templates", "QuestionBank", "Health Facility Template.xlsx");

        public static readonly string ShellTableDataTemplateFilePath = Path.Combine("Templates", "ShellTable", "ShellTableDataTemplate.xlsx");

        public static readonly string DqaIntroductionTab_EN = Path.Combine("Templates", "DQA", "DeskLevelDQA_introduction_tab_EN.xlsx");

        public static readonly string DqaIntroductionTab_FR = Path.Combine("Templates", "DQA", "DeskLevelDQA_introduction_tab_FR.xlsx");

        public const string HealthFacilitySheetName = "List of Health Facilities";

        public const string ScoreCardFileName = "Score card";

        public const string InvalidSheetName = "The uploaded file contains invalid sheet name.";

        public const string IntroductionTabSourceSheetName_EN = " Introduction and instructions";

        public const string IntroductionTabSourceSheetName_FR = " Introduction et instructions";

        // Column name Contants in EN
        public const string Province = "Province";
        public const string PublicPrivate = "Public / Private";
        public const string HealthFacility = "HealthFacility";
        public const string HealthFacilityName = "HealthFacilityName";
        public const string District = "District";
        public const string Total = "Total";
        public const string Year = "Year";

        public const string NumReportsReceivedALLcorevariablescompleted = "Number of reports received in a specified time period with ALL core variables completed";
        public const string NumReportsReceivedSpecifiedTimePeriod = "Number of reports received in the same specified time period";
        public const string CompletenessofkeyVariablesReports = "Completeness of key variables within reports (%)";

        public const string RDTtestedNumberRDTpositive = "RDT tested >/= Number of RDT positive";
        public const string MicroscopytestedMicroscopypositive = "Microscopy tested >/= Microscopy positive";
        public const string AllCauseOutpatientsTotalMalariaCases = "All cause outpatients > Total malaria cases (presumed + confirmed)";
        public const string AllcauseInpatientsMalariaInpatients = "All cause inpatients > Malaria inpatients";
        public const string AllcausedeathsMalariaInpatientDeaths = "All cause deaths > Malaria inpatient deaths";
        public const string ConfirmedmalariacasesConfirmedMalariaCasestreatedwith1stline = "Confirmed malaria cases >/= Confirmed malaria cases treated with 1st line treatment courses (incl ACT)";
        public const string SuspectedcasesMicroscopypositiveRDTpositive = "Suspected cases >/= Microscopy positive +RDT positive";
        public const string NumbeReportsReceivedSpecifiedTimePeriodWhereALLConsistencyChecks = "Number of reports received in a specified time period where ALL consistency checks between core variables are passed";
        public const string NumberReportsReceivedSpecifiedTimePeriodFromTwoDifferent = "Number of reports received in a specified time period from two different data sources where 100% of core variables match";
        public const string SuspectedCasesMicroscopyPositiveRDTpositive = "Suspected cases >/= Microscopy positive +RDT positive";
        public const string NumberPairedReportsFromTwoDataSources = "Number of paired reports from the two data sources";

    }

    public static class DataAnalysisExport
    {
        public const string DeskReviewAndDQAResult = "DataAnalysisExport.DeskReviewAndDQAResult";
        public const string SurveyResult = "DataAnalysisExport.SurveyResult";
        public const string ServiceDeliveryResult = "DataAnalysisExport.ServiceDeliveryResult";
        public const string ScoreCardFileName = "DataAnalysisExport.ScoreCardFileName";
        public const string AssessmentYear = "DataAnalysisExport.AssessmentYear";
        public const string Met = "Common.Met";
        public const string NotMet = "Common.NotMet";
        public const string PartiallyMet = "Common.PartiallyMet";
        public const string NotAssessed = "Common.NotAssessed";
    }
}
