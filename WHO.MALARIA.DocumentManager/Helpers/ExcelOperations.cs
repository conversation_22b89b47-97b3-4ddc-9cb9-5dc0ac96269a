﻿using ClosedXML.Excel;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using WHO.MALARIA.Domain.CustomAttribute;
using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.DocumentManager.Helpers
{
    /// <summary>
    /// This class contains method list of excel operations
    /// </summary>
    public static class ExcelOperations
    {
        /// <summary>
        /// This method is useful to read excel file or we can say import operation
        /// </summary>
        public static List<T> ReadExcel<T>(IFormFile file, string sheetName, int skipExcelRows, int headerColumnStartsFrom, bool matchByModelPropertyValue, List<DQAVariableDto> excelColumns)
        {
            List<T> list = new List<T>();

            Type typeOfObject = typeof(T);

            using (var memorystream = new MemoryStream())
            {
                file.CopyTo(memorystream);
                {
                    using (IXLWorkbook workbook = new XLWorkbook(memorystream))
                    {
                        IXLWorksheet worksheet = workbook.Worksheets.Where(w => w.Name == sheetName).FirstOrDefault();

                        if (worksheet == null)
                        {
                            var availableSheets = string.Join(", ", workbook.Worksheets.Select(w => w.Name));
                            throw new InvalidOperationException($"Worksheet '{sheetName}' not found in the uploaded file. Available sheets: {availableSheets}");
                        }

                        PropertyInfo[] properties = typeOfObject.GetProperties();
                        //header column texts
                        //indexing in closedxml starts with headerColumnStartsFrom not from 0
                        var columns = worksheet.Row(headerColumnStartsFrom).Cells().Select((v, i) => new { Value = v.Value, Index = i + 1 });

                        //Skip number of rows which is used for column header texts
                        foreach (IXLRow row in worksheet.RowsUsed().Skip(skipExcelRows))
                        {
                            T instanceObject = (T)Activator.CreateInstance(typeOfObject);

                            foreach (var property in properties)
                            {
                                int? colIndex = 0;
                                if (matchByModelPropertyValue)
                                {
                                    // check with proerty attribute guid with database guid
                                    DQAVariableDto excelColumn = new DQAVariableDto();
                                    List<dynamic> objectAttributes = property.GetCustomAttributes(true).ToList();

                                    if (objectAttributes.Count>0)
                                    {
                                        objectAttributes.ForEach(objectAttribute =>
                                        {
                                            GuidAttribute guidAttribute = objectAttribute as GuidAttribute;

                                            if (guidAttribute != null)
                                            {
                                                excelColumn = excelColumns.FirstOrDefault(x => x.Id == new Guid(guidAttribute.ColumnGuid));
                                                colIndex = columns.SingleOrDefault(c => c.Value.ToString() == excelColumn.Name)?.Index;
                                            }

                                        });
                                    }
                                    else
                                    {
                                        colIndex=null;
                                    }
                                }
                                else
                                {
                                    colIndex = columns.SingleOrDefault(c => c.Value.ToString() == property.Name.ToString())?.Index;
                                }

                                if (colIndex != null)
                                {
                                    var propertyValue = row.Cell(colIndex ?? 0).Value;
                                    var propertyType = property.PropertyType;

                                    property.SetValue(instanceObject, Convert.ChangeType(propertyValue, propertyType));
                                }
                                else
                                {
                                    property.SetValue(instanceObject, null);
                                }
                            }

                            list.Add(instanceObject);
                        }
                    }
                }
            }

            return list;
        }
    }
}
