﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Globalization;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Features;
using DocumentFormat.OpenXml.Spreadsheet;
using System;

namespace WHO.MALARIA.Services.Subscribers
{
    /// <summary>
    /// Handles the notification invitation email to be sent to the user
    /// </summary>
    public class UserInvitationEmailNotificationHandler : INotificationHandler<UserInvitationEmailNotification>
    {
        private readonly ILogger<UserInvitationEmailNotification> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IGraphService _graphService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ITranslationService _translationService;
        private readonly IEmailService _emailService;
        private readonly AppSettings _appSettings;

        public UserInvitationEmailNotificationHandler(ILogger<UserInvitationEmailNotification> logger,
                                                      IUnitOfWork unitOfWork,
                                                      IGraphService graphService,
                                                      IHttpContextAccessor httpContextAccessor,
                                                      ITranslationService translationService,
                                                      IEmailService emailService,
                                                      AppSettings appSettings)
        {
            _logger = logger;
            _unitOfWork = unitOfWork;
            _graphService = graphService;
            _httpContextAccessor = httpContextAccessor;
            _translationService = translationService;
            _emailService = emailService;
            _appSettings = appSettings;
        }

        /// <summary>
        /// Fetches the email template, formats the email body, and sends the email to the user.
        /// </summary>
        /// <param name="notification">Object of UserInvitationEmailNotification class</param>
        /// <param name="cancellationToken">Used to cancel the current operation</param>
        public async Task Handle(UserInvitationEmailNotification notification, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting user invitation email process for {Email} by {CurrentUser}", notification.Email, notification.CurrentUserName);

            try
            {
                EmailTemplate emailTemplate = await _unitOfWork.EmailTemplateRepository
                                                               .Queryable(et => et.Type == (int)EmailTemplateType.UserActivation)
                                                               .SingleAsync();

                if (emailTemplate == null)
                {
                    _logger.LogError("Email template for UserActivation not found");
                    throw new InvalidOperationException("Email template for user activation not found");
                }

                // Use configured BaseUrl instead of current request URL to ensure correct domain in invitation links
                string applicationUrl = _appSettings?.AzureAD?.BaseUrl ?? _httpContextAccessor.HttpContext.GetApplicationURL();
                string callBackUrl = $"{applicationUrl}/user/invitation/accept/{notification.UserCountryAccessId}";

                _logger.LogInformation("Sending Azure AD invitation for {Email} with callback URL {CallbackUrl}", notification.Email, callBackUrl);

                Tuple<SendInvitationStatus, string> invitationDetails = await _graphService.SendInvitation(
                    notification.Name,
                    string.Empty,
                    notification.Email,
                    emailTemplate.Body,
                    callBackUrl,
                    notification.CurrentUserName,
                    CultureInfo.CurrentCulture.Name);

                switch (invitationDetails.Item1)
                {
                    case SendInvitationStatus.InsufficientPrivileges:
                        _logger.LogError("Insufficient privileges to send invitation to {Email}", notification.Email);
                        throw new InSufficiantPermissionException(_translationService.GetTranslatedMessage(Constants.Exception.InSufficientPrivilegesToSendInvite));

                    case SendInvitationStatus.BadRequest:
                        _logger.LogError("Bad request when sending invitation to {Email} - may be same domain as Azure AD", notification.Email);
                        throw new InSufficiantPermissionException(_translationService.GetTranslatedMessage(Constants.Exception.SameDomainAsAzureADCannotBeInvited));

                    case SendInvitationStatus.InvitedSuccesfully:
                        {
                            if (!string.IsNullOrEmpty(invitationDetails.Item2))
                            {
                                try
                                {
                                    string formattedEmailBody = string.Format(emailTemplate.Body, notification.Country, invitationDetails.Item2);

                                    _logger.LogInformation("Sending invitation email to {Email}", notification.Email);
                                    await _emailService.SendEmail(new string[] { notification.Email }, Domain.Constants.Constants.Common.InvitationSubject, formattedEmailBody, true);

                                    _logger.LogInformation("User invitation process completed successfully for {Email}", notification.Email);
                                }
                                catch (Exception emailEx)
                                {
                                    _logger.LogError(emailEx, "Failed to send invitation email to {Email}: {Message}", notification.Email, emailEx.Message);
                                    throw new InvalidOperationException($"Failed to send invitation email to {notification.Email}: {emailEx.Message}", emailEx);
                                }
                            }
                            else
                            {
                                _logger.LogError("Azure AD invitation succeeded but redeem URL is empty for {Email}", notification.Email);
                                throw new InvalidOperationException("Azure AD invitation succeeded but redeem URL is empty");
                            }
                        }
                        break;

                    default:
                        _logger.LogError("Unknown invitation status {Status} for {Email}", invitationDetails.Item1, notification.Email);
                        throw new InvalidOperationException($"Unknown invitation status: {invitationDetails.Item1}");
                }
            }
            catch (Exception ex) when (!(ex is InSufficiantPermissionException))
            {
                _logger.LogError(ex, "Unexpected error in user invitation process for {Email}: {Message}", notification.Email, ex.Message);
                throw;
            }
        }
    }
}
