﻿using MediatR;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Upload malaria toolkit document
    /// </summary>
    public class UploadDocumentCommandHandler : RuleBase, IRequestHandler<UploadDocumentCommand, bool>
    {

        private readonly ITranslationService _translationService;
        private readonly ICommonRuleChecker _commonRuleChecker;

        public UploadDocumentCommandHandler(ITranslationService translationService,
                                            ICommonRuleChecker commonRuleChecker
                                           )
        {
            _translationService = translationService;
            _commonRuleChecker = commonRuleChecker;
        }

        /// <summary>
        /// Performs validations on the request,save uploaded malaria toolkit document
        /// </summary>
        /// <param name="request">Contains document to upload </param>
        /// <param name="cancellationToken">Notifies to cancel the operation</param>
        /// <returns>True if saved successfully</returns>
        public async Task<bool> Handle(UploadDocumentCommand request, CancellationToken cancellationToken)
        {
            //Check business rules       
            string fileExtension = Path.GetExtension(request.File.FileName);

            CheckRule(new IsFileExtensionValidRule(_translationService, fileExtension, Constants.Common.ValidZipFileExtensions));

            CheckRule(new FileNameCheckRule(_translationService, request.File.FileName, Constants.Common.DocumentFileName, Constants.Exception.MalariaToolKitFileName));

            string existingServerFileToDeletePath = Path.Combine(Environment.CurrentDirectory, Constants.Common.DocumentUploadFilePath, request.Language, Constants.Common.DocumentFileName);
            string serverFileUploadedPath = Path.Combine(Environment.CurrentDirectory, Constants.Common.DocumentUploadFilePath, request.Language);

            if (File.Exists(existingServerFileToDeletePath))
            {
                File.Delete(existingServerFileToDeletePath);
            }

            using (FileStream stream = new FileStream(Path.Combine(serverFileUploadedPath, request.File.FileName), FileMode.Create))
            {
                await request.File.CopyToAsync(stream);
            }

            return true;
        }
    }
}