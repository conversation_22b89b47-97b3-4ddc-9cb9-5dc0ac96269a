using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check if user is a manager or super manager of a specific country
    /// </summary>
    public class UserShouldBeManagerOrSuperManagerOfCountryRule : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly Guid _userId;
        private readonly Guid _countryId;
        private readonly string _countryName;
        private readonly ITranslationService _translationService;

        public UserShouldBeManagerOrSuperManagerOfCountryRule(
            ITranslationService translationService,
            IUserRuleChecker ruleChecker,
            Guid userId,
            Guid countryId,
            string countryName = "")
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _userId = userId;
            _countryId = countryId;
            _countryName = countryName;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.UserMustBeManagerOrSuperManagerOfCountry) + _countryName;

        public bool IsBroken() => !_ruleChecker.IsUserManagerOrSuperManagerOfCountry(_userId, _countryId);
    }
}
