using System;
using WHO.MALARIA.Domain;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using Constants = WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Services.Rules.User
{
    /// <summary>
    /// Rule to check if user is a manager or super manager
    /// </summary>
    public class UserShouldBeManagerOrSuperManagerRule : IBusinessRule
    {
        private readonly IUserRuleChecker _ruleChecker;
        private readonly Guid _userId;
        private readonly ITranslationService _translationService;

        public UserShouldBeManagerOrSuperManagerRule(
            ITranslationService translationService, 
            IUserRuleChecker ruleChecker,
            Guid userId)
        {
            _translationService = translationService;
            _ruleChecker = ruleChecker;
            _userId = userId;
        }

        public string Message => _translationService.GetTranslatedMessage(Constants.Exception.OnlyManagerOrSuperManagerAccess);
        public bool IsBroken() => !_ruleChecker.IsUserManagerOrSuperManager(_userId);
    }
}
