﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Services.BusinessRuleValidations.Interfaces
{
    public interface IUserRuleChecker
    {
        bool IsUserSuperManager(Guid userId);
        public bool IsUserViewer(Guid userId);
        bool DoesCountryHaveSuperManager(Guid countryId);
        bool IsUserStatusPending(string email);
        bool IsUserWHOAdmin(Guid userId);
        Task<UserExistsInAzureDirectoryStatus> IsUserInWHOActiveDirectory(string userEmail, string currentUserEmail);
        bool IsUserSuperManagerOfCountry(Guid userId, Guid countryId);
        bool CheckForDuplicateUserCountryAccess(Guid userId, Guid[] countryRequestedIds);
        bool HasDifferentSuperManagerAssignedToCountry(Guid userId, Guid countryId);
        bool IsUserManagerOfAnyInProgressAssessment(Guid userId);
        bool IsUserManager(Guid userId);
        bool IsUserManagerOrSuperManager(Guid userId);
        bool IsUserManagerOrSuperManagerOfCountry(Guid userId, Guid countryId);
    }
}
