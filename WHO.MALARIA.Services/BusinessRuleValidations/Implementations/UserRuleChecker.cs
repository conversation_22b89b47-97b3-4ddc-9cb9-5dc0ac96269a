﻿using System;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;

namespace WHO.MALARIA.Services.BusinessRuleValidations.Implementations
{
    public class UserRuleChecker : IUserRuleChecker
    {
        private readonly IDbManager _dbManager;
        private readonly IGraphService _graphService;

        public UserRuleChecker(IDbManager dbManager, IGraphService graphService)
        {
            _dbManager = dbManager;
            _graphService = graphService;
        }

        /// <summary>
        /// Checks if user is a super manager
        /// </summary>
        /// <param name="userId">User Id</param>
        /// <returns>Returs true if user is super manager; else false</returns>
        public bool IsUserSuperManager(Guid userId)
        {
            string sql = "SELECT TOP 1 1 FROM " +
                         $"[{MalariaSchemas.Internal}].[UserCountryAccess] AS UCA " +
                         "WHERE UCA.UserId=@UserId AND UCA.UserType = @UserType";

            int? isUserSuperManager = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                UserId = userId,
                                                UserType = (int)UserRoleEnum.SuperManager
                                            });

            return isUserSuperManager.HasValue;
        }

        /// <summary>
        /// Checks if user is a viewer
        /// </summary>
        /// <param name="userId">User Id</param>
        /// <returns>Returs true if user is viewer; else false</returns>
        public bool IsUserViewer(Guid userId)
        {
            string sql = "SELECT TOP 1 1 FROM " +
                         $"[{MalariaSchemas.Internal}].[UserCountryAccess] AS UCA " +
                         "WHERE UCA.UserId=@UserId AND UCA.UserType = @UserType";

            int? isUserViewer = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                UserId = userId,
                                                UserType = (int)UserRoleEnum.Viewer
                                            });

            return isUserViewer.HasValue;
        }


        /// <summary>
        ///  Checks if country already has super manager
        /// </summary>
        /// <param name="countryId">Country Id</param>
        /// <returns>Returs true if country has super manager; else false</returns>
        public bool DoesCountryHaveSuperManager(Guid countryId)
        {
            string sql = "SELECT TOP 1 1 " +
                        $"FROM [{MalariaSchemas.Internal}].[UserCountryAccess] AS UCA " +
                        "WHERE UCA.[CountryId] = @CountryId AND UCA.UserType = @UserType AND UCA.Status=1";

            int? countryHasSuperManager = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                CountryId = countryId,
                                                UserType = (int)UserRoleEnum.SuperManager
                                            });

            return countryHasSuperManager.HasValue;
        }

        /// <summary>
        ///  Checks if user status is pending
        /// </summary>
        /// <param name="userEmail">Email of the user</param>
        /// <returns>Returs true if user status is pending; else false</returns>
        public bool IsUserStatusPending(string userEmail)
        {
            string sql = $@"SELECT TOP 1 1
                          FROM [{MalariaSchemas.Internal}].[User] AS U 
                          JOIN [{MalariaSchemas.Internal}].[Identity] AS I ON U.IdentityId=I.Id 
                          WHERE I.Email=@UserEmail AND U.Status=@PendingStatus";

            int? found = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                UserEmail = userEmail,
                                                PendingStatus = (int)UserStatus.Pending
                                            });

            return found.HasValue;
        }

        /// <summary>
        ///  Checks if user is a WHO admin
        /// </summary>
        /// <param name="userId">User Id</param>
        /// <returns>Returs true if user is a WHO admin; else false</returns>
        public bool IsUserWHOAdmin(Guid userId)
        {            
            string sql = "SELECT TOP 1 1 FROM " +
                         $"[{MalariaSchemas.Internal}].[User] AS U " +
                         "WHERE U.Id=@UserId AND U.IsWhoAdmin = 1";

            int? isWhoUser = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                UserId = userId
                                            });

            return isWhoUser.HasValue;
        }

        /// <summary>
        /// Checks if user is a super manager of a country
        /// </summary>
        /// <param name="userId">User Id for which rule has to be checked</param>
        /// <param name="countryId">Id of countries</param>
        /// <returns>Returns true if user is super manager of a country; else false</returns>
        public bool IsUserSuperManagerOfCountry(Guid userId, Guid countryId)
        {
            string sql = @$"SELECT TOP 1 1 
                            FROM [{MalariaSchemas.Internal}].[UserCountryAccess] AS UCA
                            WHERE UCA.UserId=@UserId AND UCA.UserType = @UserType AND UCA.CountryId=@CountryId";

            int? isUserSuperManager = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                UserId = userId,
                                                UserType = (int)UserRoleEnum.SuperManager,
                                                CountryId = countryId
                                            });

            return isUserSuperManager.HasValue;
        }

        /// <summary>
        /// Checks if user is in WHO active directory
        /// </summary>
        /// <param name="userEmail">Email of the user who has to be checked</param>
        /// <param name="currentUserEmail">Email of the current user</param>
        /// <returns>Returns true if user is in WHO active directory; else false</returns>
        public async Task<UserExistsInAzureDirectoryStatus> IsUserInWHOActiveDirectory(string userEmail, string currentUserEmail)
        {
            UserExistsInAzureDirectoryStatus status = await _graphService.IsUserExistingInAzureAD(userEmail, currentUserEmail);

            return status;
        }

        /// <summary>
        /// Checks if same country has already been requested by the user validation
        /// </summary>
        /// <param name="userId">User id who request for the country access</param>
        /// <param name="countryRequestedIds">List of country ids which user request access for</param>
        /// <returns>Returns true if user already requested for access of any country which passed in list of countries</returns>
        public bool CheckForDuplicateUserCountryAccess(Guid userId, Guid[] countryRequestedIds)
        {
            string sql = @$"SELECT TOP 1 1 
                            FROM [{MalariaSchemas.Internal}].[UserCountryAccess] AS UCA 
                            INNER JOIN STRING_SPLIT(@CountryIds,',') T ON UCA.CountryId = T.value 
                            WHERE UCA.UserId=@UserId AND UCA.[Status]<>@Status";

            int? isDuplicateUserCountryAceess = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                UserId = userId,
                                                CountryIds = string.Join(',', countryRequestedIds),
                                                Status = (int)UserCountryAccessRightsEnum.Rejected
                                            });

            return isDuplicateUserCountryAceess.HasValue;
        }

        /// <summary>
        /// Check if for a given country is there a super manager assigned except the current user.
        /// </summary>
        /// <param name="userId">Country should not be assigned to this user id</param>
        /// <param name="countryId">Assigned country id of the user</param>
        /// <returns>True if country has another user assigned else False</returns>
        public bool HasDifferentSuperManagerAssignedToCountry(Guid userId, Guid countryId)
        {
            string sql = @$"SELECT TOP 1 1 
                            FROM [{MalariaSchemas.Internal}].[UserCountryAccess] AS UCA
                            WHERE UCA.UserId <> @UserId AND UCA.UserType = @UserType AND UCA.CountryId=@CountryId AND ( UCA.[Status] = @AcceptedStatus OR UCA.[Status] = @InvitationNotAcceptedStatus)";

            int? hasDifferentSuperManager = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                UserId = userId,
                                                UserType = (int)UserRoleEnum.SuperManager,
                                                CountryId = countryId,
                                                AcceptedStatus =  UserCountryAccessRightsEnum.Accepted, 
                                                InvitationNotAcceptedStatus= UserCountryAccessRightsEnum.InvitationNotAccepted
                                            });

            return hasDifferentSuperManager.HasValue;
        }

        /// <summary>
        /// Checks if the user is a manager of any in-progress assessment
        /// </summary>
        /// <param name="userId">User Id for which rule has to be checked</param>     
        /// <returns>Returns true if user is active manager of any in-progress assessment; else false</returns>
        public bool IsUserManagerOfAnyInProgressAssessment(Guid userId)
        {
            string sql = @$"SELECT TOP 1 1 
                            FROM [{MalariaSchemas.ScopeDefinition}].[Assessment] A
                            INNER JOIN [{MalariaSchemas.ScopeDefinition}].AssessmentStatus AST ON A.Id = AST.AssessmentId
                            INNER JOIN [{MalariaSchemas.ScopeDefinition}].AssessmentUser AU ON AU.AssessmentId = A.Id
                            INNER JOIN [{MalariaSchemas.Internal}].[UserCountryAccess] AS UCA ON UCA.CountryId = A.CountryId
                            WHERE UCA.UserId = @UserId AND UCA.UserType = @UserType AND UCA.Status = @UserCountryAccessStatus AND AST.Status!= @AssessmentStatus AND AU.UserId = @UserId";

            int? isUserManager = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                UserId = userId,
                                                UserType = (int)UserRoleEnum.Manager,
                                                UserCountryAccessStatus = (int)UserCountryAccessRightsEnum.Accepted,
                                                AssessmentStatus = (int) Domain.Enum.AssessmentStatus.Published
                                            });

            return isUserManager.HasValue;
        }

        /// <summary>
        /// Checks if user is a manager
        /// </summary>
        /// <param name="userId">User Id</param>
        /// <returns>Returns true if user is manager; else false</returns>
        public bool IsUserManager(Guid userId)
        {
            string sql = "SELECT TOP 1 1 FROM " +
                         $"[{MalariaSchemas.Internal}].[UserCountryAccess] AS UCA " +
                         "WHERE UCA.UserId=@UserId AND UCA.UserType = @UserType";

            int? isUserManager = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                UserId = userId,
                                                UserType = (int)UserRoleEnum.Manager
                                            });

            return isUserManager.HasValue;
        }

        /// <summary>
        /// Checks if user is a manager or super manager
        /// </summary>
        /// <param name="userId">User Id</param>
        /// <returns>Returns true if user is manager or super manager; else false</returns>
        public bool IsUserManagerOrSuperManager(Guid userId)
        {
            string sql = "SELECT TOP 1 1 FROM " +
                         $"[{MalariaSchemas.Internal}].[UserCountryAccess] AS UCA " +
                         "WHERE UCA.UserId=@UserId AND (UCA.UserType = @ManagerType OR UCA.UserType = @SuperManagerType)";

            int? isUserManagerOrSuperManager = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                UserId = userId,
                                                ManagerType = (int)UserRoleEnum.Manager,
                                                SuperManagerType = (int)UserRoleEnum.SuperManager
                                            });

            return isUserManagerOrSuperManager.HasValue;
        }

        /// <summary>
        /// Checks if user is a manager or super manager of a country
        /// </summary>
        /// <param name="userId">User Id for which rule has to be checked</param>
        /// <param name="countryId">Id of countries</param>
        /// <returns>Returns true if user is manager or super manager of a country; else false</returns>
        public bool IsUserManagerOrSuperManagerOfCountry(Guid userId, Guid countryId)
        {
            string sql = @$"SELECT TOP 1 1
                            FROM [{MalariaSchemas.Internal}].[UserCountryAccess] AS UCA
                            WHERE UCA.UserId=@UserId AND (UCA.UserType = @ManagerType OR UCA.UserType = @SuperManagerType) AND UCA.CountryId=@CountryId";

            int? isUserManagerOrSuperManager = _dbManager.QuerySingleOrDefault<int?>(sql,
                                            new
                                            {
                                                UserId = userId,
                                                ManagerType = (int)UserRoleEnum.Manager,
                                                SuperManagerType = (int)UserRoleEnum.SuperManager,
                                                CountryId = countryId
                                            });

            return isUserManagerOrSuperManager.HasValue;
        }
    }
}
