﻿using System;
using System.Linq;

using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.SeedingMetadata;

namespace WHO.MALARIA.Database.Seeding
{
    /// <summary>
    /// Seeding data of EmailTemplateSeeding.
    /// </summary>
    public static class EmailTemplateSeeding
    {
        /// <summary>
        /// Save countries in EmailTemplateSeeding table
        /// </summary>
        /// <param name="malariaDbContext">instance of MalariaDbContext</param>
        public static void Seed(MalariaDbContext malariaDbContext)
        {
            IQueryable<EmailTemplate> existingRecords = malariaDbContext.EmailTemplates.AsQueryable();

            EmailTemplate[] newRecords = new EmailTemplate[]
                {
                       new EmailTemplate
                       {
                           Id = EmailTemplateSeedingMetadata.UserInvitationTemplate_ID,
                           Subject="Invitation for Malaria Toolkit Application",
                           //New email Template added for sending invitation.
                           //Body=$"<!DOCTYPE html><html><body>Hello {{0}},<br/><br/>Congratulations!<br/><br/>You have been invited to WHO-MALARIA Surveillance Toolkit.<br/>Kindly accept the invitation and begin using the application for country {{1}} by logging in.<br/><br/>Click below link to log in:<br/><br/><a style=\"background: #48bf53; cursor: pointer; color: #ffffff; font-size: 14px; font-weight: 700; border-radius: 8px; line-height: 42px; padding: 8px;  text-decoration: none;\" href=\"{{2}}\">Accept Invitation</a></body></html>",
                           Body=$"<!DOCTYPE html><html><body>Hello,<br/><br/>Congratulations!<br/><br/>You have been invited to WHO-MALARIA Surveillance Toolkit.<br/>Kindly accept the invitation and begin using the application for country {{0}} by logging in.<br/><br/>Click below link to log in:<br/><br/><a style=\"background: #48bf53; cursor: pointer; color: #ffffff; font-size: 14px; font-weight: 700; border-radius: 8px; line-height: 42px; padding: 8px;  text-decoration: none;\" href=\"{{1}}\" target=\"_blank\" rel=\"noopener noreferrer\">Accept Invitation</a><br/><br/>“If you have trouble using the \"Accept invitation\" button, please try the following:<br/>  1. Copy the link below.<br/>  2. Open a new browser window or tab.<br/>  3. Paste the link into the address bar and press Enter.<br/><br/>  <a href=\"{{1}}\" style=\"color: blue; text-decoration: underline;\">{{1}}</a></body></html>",
                           Type = (int)EmailTemplateType.UserActivation,
                           CreatedBy = null,
                           CreatedAt =DateTime.Now
                       },

                       new EmailTemplate
                       {
                           Id = EmailTemplateSeedingMetadata.UserActivationRequestRejectionTemplate_ID,
                           Subject="Malaria Toolkit application access denial",
                           Body=$"Hello,{Environment.NewLine}{Environment.NewLine}We are really sorry to inform you that your access has been denied for Malaria Toolkit application for below country.{Environment.NewLine}Country: {{0}}{Environment.NewLine}Role: {{1}}{Environment.NewLine}Status: Access denied{Environment.NewLine}Reasons: {{2}}{Environment.NewLine}{Environment.NewLine}Regards,{Environment.NewLine}WHO Malaria Surveillance Toolkit Team",
                           Type = (int)EmailTemplateType.UserActivationRequestRejection,
                           CreatedBy = null,
                           CreatedAt = DateTime.Now
                       },

                       new EmailTemplate
                       {
                           Id = EmailTemplateSeedingMetadata.DeactivateViewerUserTemplate_ID,
                           Subject="WHO Malaria Surveillance Toolkit: User Deactivation Notification",
                           Body=$"Hello,{Environment.NewLine}{Environment.NewLine}The following role has been revoked for below country{Environment.NewLine}Country: {{0}}{Environment.NewLine}Role: {{1}}{Environment.NewLine}Status: {{2}}{Environment.NewLine}{Environment.NewLine}Comments/Reasons:{Environment.NewLine}Super Manager of this country has revoked your access for this country.{Environment.NewLine}{Environment.NewLine}Regards,{Environment.NewLine}WHO Malaria Surveillance Toolkit Team",
                           Type = (int)EmailTemplateType.DeactivateViewerUser,
                           CreatedBy = null,
                           CreatedAt = DateTime.Now
                       },

                       new EmailTemplate
                       {
                           Id = EmailTemplateSeedingMetadata.ActivateViewerUserTemplate_ID,
                           Subject="WHO Malaria Surveillance Toolkit: User Activation Notification",
                           Body=$"Hello,{Environment.NewLine}{Environment.NewLine}The following role has been assigned for below country{Environment.NewLine}Country: {{0}}{Environment.NewLine}Role: {{1}}{Environment.NewLine}Status: {{2}}{Environment.NewLine}{Environment.NewLine}Comments/Reasons:{Environment.NewLine}Super Manager of this country has assigned you as a {{3}}.{Environment.NewLine}{Environment.NewLine}Regards,{Environment.NewLine}WHO Malaria Surveillance Toolkit Team",
                           Type = (int)EmailTemplateType.ActivateViewerUser,
                           CreatedBy = null,
                           CreatedAt = DateTime.Now
                       },

                       new EmailTemplate
                       {
                           Id = EmailTemplateSeedingMetadata.DeActivatedUserForAllAssignedCountriesTemplate_ID,
                           Subject="WHO Malaria Surveillance Toolkit: Application Access Revoked",
                           Body=$"Hello,{Environment.NewLine}{Environment.NewLine}Your access to Malaria Surveillance Toolkit application has been removed as you are deactivated for previously assigned countries. You will no longer be able to access this application.{Environment.NewLine}Status: Deactivated{Environment.NewLine}{Environment.NewLine}Comments/Reasons:{Environment.NewLine}Deactivated for previously assigned countries.{Environment.NewLine}{Environment.NewLine}Regards,{Environment.NewLine}WHO Malaria Surveillance Toolkit Team",
                           Type = (int)EmailTemplateType.DeactivatedUserForAllAssignedCountries,
                           CreatedBy = null,
                           CreatedAt = DateTime.Now
                       },

                       new EmailTemplate
                       {
                            Id = EmailTemplateSeedingMetadata.UserAssignedToAssessmentTemplate_ID,
                            Subject = "WHO Malaria Surveillance Toolkit: Assessment Role Notification",
                            Body= $"Hello,{Environment.NewLine}{Environment.NewLine}The following role has been assigned to you for below country assessment{Environment.NewLine}Country: {{0}}{Environment.NewLine}Role: {{1}}{Environment.NewLine}{Environment.NewLine}Comments/Reasons{Environment.NewLine}Super Manager/Manager of this country has assigned you as {{2}} for the assessment.{Environment.NewLine}{Environment.NewLine}Regards,{Environment.NewLine}WHO Malaria Surveillance Toolkit Team",
                            Type = (int)EmailTemplateType.UserAssignedToAssessment,
                            CreatedBy = null,
                            CreatedAt = DateTime.Now
                       },

                       new EmailTemplate
                        {
                            Id = EmailTemplateSeedingMetadata.UserRemovedFromAssessmentTemplate_ID,
                            Subject = "WHO Malaria Surveillance Toolkit: Assessment Role Notification",
                            Body = $"Hello,{Environment.NewLine}{Environment.NewLine}Kindly note that you have been removed from the role for an assessment{Environment.NewLine}Country: {{0}}{Environment.NewLine}Role: {{1}}{Environment.NewLine}{Environment.NewLine}Comments/Reasons:{Environment.NewLine}Super Manager/Manager of this country has removed you as {{2}} for the assessment.{Environment.NewLine}{Environment.NewLine}Regards,{Environment.NewLine}WHO Malaria Surveillance Toolkit Team",
                            Type = (int)EmailTemplateType.UserRemovedFromAssessment,
                            CreatedBy = null,
                            CreatedAt = DateTime.Now
                        },

                       new EmailTemplate
                        {
                            Id = EmailTemplateSeedingMetadata.UserCountryAccessGrantedTemplate_ID,
                            Subject = "WHO Malaria Surveillance Toolkit: User Country Access Granted",
                            Body = $"Hello,{Environment.NewLine}{Environment.NewLine}Application access has been granted to you for below country{Environment.NewLine}Country: {{0}}{Environment.NewLine}Role: Viewer{Environment.NewLine}Status: Activated{Environment.NewLine}{Environment.NewLine}Comments/Reasons:{Environment.NewLine}Super Manager of this country has assigned you as a Viewer.{Environment.NewLine}{Environment.NewLine}Regards,{Environment.NewLine}WHO Malaria Surveillance Toolkit Team{Environment.NewLine}{Environment.NewLine}Click below link to log in:{Environment.NewLine}{{1}}",
                            Type = (int)EmailTemplateType.UserCountryAccessGranted,
                            CreatedBy = null,
                            CreatedAt = DateTime.Now
                        },

                       new EmailTemplate
                        {
                           Id = EmailTemplateSeedingMetadata.SuperManagerRoleAssignmentTemplate_ID,
                           Subject="WHO Malaria Surveillance Toolkit: Super Manager role assignment notification",
                           Body=$"Hello,{Environment.NewLine}{Environment.NewLine}The following role has been assigned to you for below country{Environment.NewLine}Country: {{0}}{Environment.NewLine}Role: Super Manager {Environment.NewLine}{Environment.NewLine}Comments/Reasons:{Environment.NewLine}WHO Admin of malaria surveillance toolkit has assigned you as Super Manager for the country {{0}}. {Environment.NewLine}{Environment.NewLine}Regards,{Environment.NewLine}WHO Malaria Surveillance Toolkit Team {Environment.NewLine} {Environment.NewLine}Click below link to log in:{Environment.NewLine}{{1}}",
                           Type = (int)EmailTemplateType.SuperManagerRoleAssignment,
                           CreatedBy = null,
                           CreatedAt = DateTime.Now
                        },

                       new EmailTemplate
                        {
                           Id = EmailTemplateSeedingMetadata.WHOAdminRoleAssignmentTemplate_ID,
                           Subject="WHO Malaria Surveillance Toolkit: WHO Admin role assignment notification",
                           Body=$"Hello,{Environment.NewLine}{Environment.NewLine}The following role has been assigned to you for malaria surveillance toolkit application.{Environment.NewLine}Country: All countries {Environment.NewLine}Role: WHO Admin {Environment.NewLine}{Environment.NewLine}Comments/Reasons:{Environment.NewLine}You have been assigned as WHO Admin for malaria surveillance toolkit application. {Environment.NewLine}{Environment.NewLine}Regards,{Environment.NewLine}WHO Malaria Surveillance Toolkit Team {Environment.NewLine} {Environment.NewLine}Click below link to log in:{Environment.NewLine}{{1}}",
                           Type = (int)EmailTemplateType.WHOAdminRoleAssignment,
                           CreatedBy = null,
                           CreatedAt = DateTime.Now
                        },
                        new EmailTemplate
                        {
                           Id = EmailTemplateSeedingMetadata.AccessRequestForCountryFromWHOUserTemplate_ID,
                           Subject="WHO Malaria Surveillance Toolkit: User Country Access Requested",
                           Body = $"Hello,{Environment.NewLine}{Environment.NewLine}Country access has been requested for country {{0}}{Environment.NewLine}Please Grant or Reject the request by logging into the system{Environment.NewLine}{Environment.NewLine}Regards,{Environment.NewLine}WHO Malaria Surveillance Toolkit Team{Environment.NewLine}{Environment.NewLine}Click below link to log in:{Environment.NewLine}{{1}}",
                           Type = (int)EmailTemplateType.AccessRequestForCountryFromWHOUser,
                           CreatedBy = null,
                           CreatedAt = DateTime.Now
                        },

                    };

            // 1. delete the existing record in the database if they are not in the new records
            foreach (EmailTemplate existingRecord in existingRecords)
            {
                if (!newRecords.Any(nr => nr.Id == existingRecord.Id))
                {
                    malariaDbContext.EmailTemplates.Remove(existingRecord);
                }
            }

            foreach (EmailTemplate newRecord in newRecords)
            {
                EmailTemplate existingRecord = existingRecords.SingleOrDefault(existing => existing.Id == newRecord.Id);
                if (existingRecord != null)
                {
                    // 2.
                    // if the record already exists in the database and 
                    // is modified then update it in the database
                    if (IsRecordUpdated(existingRecord, newRecord))
                    {
                        existingRecord.Subject = newRecord.Subject;
                        existingRecord.Body = newRecord.Body;
                        existingRecord.Type = newRecord.Type;
                        existingRecord.UpdatedBy = null;
                        existingRecord.UpdatedAt = DateTime.Now;

                        malariaDbContext.EmailTemplates.Update(existingRecord);
                    }
                }
                else
                {
                    // 3. else add the new record into the database
                    malariaDbContext.EmailTemplates.Add(newRecord);
                }
            }

            //Save changes in db-context
            malariaDbContext.SaveChanges();
        }
        private static bool IsRecordUpdated(EmailTemplate existingRecord, EmailTemplate newRecord)
        {
            return (existingRecord.Subject != newRecord.Subject
                    || existingRecord.Body != newRecord.Body
                    || existingRecord.Type != newRecord.Type
                   );
        }
    }
}
