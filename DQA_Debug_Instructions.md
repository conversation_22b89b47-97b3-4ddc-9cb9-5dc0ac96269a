# DQA Template Generation and File Upload - Debug Instructions

## Issues Fixed

### 1. Frontend Error Handling Improvements
- **Service Level Template Generation**: Added detailed error logging and specific error messages
- **Desk Level Template Generation**: Added detailed error logging and specific error messages  
- **File Upload Operations**: Improved error handling for both service level and desk level uploads
- **Console Logging**: Added console.log statements to track API calls and errors

### 2. Backend Error Handling Improvements
- **DQA Controller**: Added try-catch blocks with detailed error logging for all endpoints
- **Template Generation**: Added validation to ensure file data is returned before sending response
- **File Upload Processing**: Added logging for file upload operations

### 3. Template File Validation
- **DQA Document Manager**: Added file existence checks before attempting to open Excel templates
- **Path Validation**: Added logging to show which template files are being accessed
- **Error Messages**: Specific error messages when template files are missing

### 4. Diagnostic Endpoint
- **New Endpoint**: `/api/dqa/diagnostic/templates` to check template file existence
- **File Path Verification**: Shows current directory and all template file paths with existence status

## Testing Steps

### 1. Check Template Files Existence
1. Navigate to: `{baseUrl}/api/dqa/diagnostic/templates`
2. Verify all template files show `"Exists": true`
3. If any files are missing, check the Templates/DQA folder

### 2. Test Template Generation
1. **Service Level DQA**:
   - Go to Data Collection → DQA → Service Delivery Level
   - Fill mandatory fields and click Save, then Finalize
   - Click "Generate Template" button
   - Check browser console for detailed logs
   - Verify file downloads successfully

2. **Desk Level DQA**:
   - Go to Data Collection → DQA → Desk Level
   - Fill mandatory fields and click Save, then Finalize  
   - Click "Generate Template" button
   - Check browser console for detailed logs
   - Verify file downloads successfully

### 3. Test File Upload
1. **Service Level Upload**:
   - Generate a template first
   - Try uploading the generated template
   - Check browser console and network tab for errors
   - Verify success/error messages are specific

2. **Desk Level Upload**:
   - Generate a template first
   - Try uploading the generated template
   - Check browser console and network tab for errors
   - Verify success/error messages are specific

### 4. Check Server Logs
- Look for console output in the server logs for detailed error information
- Template file paths and existence will be logged
- Any exceptions will be logged with full stack traces

## Common Issues and Solutions

### Template Files Missing
- **Symptom**: FileNotFoundException in logs
- **Solution**: Ensure all template files exist in WHO.MALARIA.Web/Templates/DQA/
- **Required Files**:
  - DQA_SERVICE_EN.xlsx
  - DQA_SERVICE_FR.xlsx
  - DeskLevelDQA_introduction_tab_EN.xlsx
  - DeskLevelDQA_introduction_tab_FR.xlsx
  - DQA_desk_level_tool_report_EN.xlsx
  - DQA_desk_level_tool_report_FR.xlsx
  - DQA_Elimination.xlsx

### Path Issues
- **Symptom**: Template files exist but still getting FileNotFoundException
- **Solution**: Check the diagnostic endpoint to verify paths are correct
- **Note**: Paths use backslashes on Windows, forward slashes on Linux/Mac

### Permission Issues
- **Symptom**: Access denied errors
- **Solution**: Ensure the application has read permissions on template files

### Excel Processing Issues
- **Symptom**: Errors during Excel file generation/processing
- **Solution**: Check if ClosedXML dependencies are properly installed
- **Note**: May need to install Microsoft.Office.Interop.Excel or similar packages

## Browser Console Commands for Testing

```javascript
// Test Service Level Template Generation
console.log("Testing Service Level Template Generation...");

// Test Desk Level Template Generation  
console.log("Testing Desk Level Template Generation...");

// Check for any JavaScript errors
console.error("Check for any errors above");
```

## Network Tab Debugging
1. Open browser Developer Tools (F12)
2. Go to Network tab
3. Perform template generation or upload
4. Look for failed requests (red status codes)
5. Check response details for specific error messages

## Next Steps if Issues Persist
1. Check the diagnostic endpoint first
2. Verify all template files exist and have correct names
3. Check server logs for detailed error messages
4. Ensure proper permissions on template files
5. Verify Excel processing libraries are installed
6. Test with different browsers to rule out client-side issues
